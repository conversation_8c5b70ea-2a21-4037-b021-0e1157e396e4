<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示文稿 - 13 页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: white;
            scroll-behavior: smooth;
        }

        .presentation-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 20px;
            background: white !important;
        }

        .slide-page {
            width: 1280px;
            min-height: 720px;
            margin: 20px auto;
            background: white !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            page-break-after: always;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
        }

        .slide-page .slide {
            width: 1280px;
            height: 720px;
            margin: 0;
            border-radius: 0;
            position: relative;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .nav-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .nav-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .slide-counter {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 1000;
        }

        .slide-navigation {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            z-index: 1000;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .slide-nav-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .slide-nav-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .slide-nav-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media print {
            .navigation, .slide-counter, .slide-navigation { display: none; }
            .slide-page { margin: 0; box-shadow: none; page-break-after: always; }
        }

        /* 原始幻灯片样式 */
        
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: 900;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
            z-index: 0;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .content {
            position: relative;
            z-index: 1;
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .menu-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .menu-item:hover {
            border-left-color: #38bdf8;
            transform: translateX(5px);
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .highlight-number {
            font-size: 3.5rem;
            font-weight: 700;
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            line-height: 1;
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }
        .data-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .highlight-box {
            border-left: 4px solid #38bdf8;
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .tech-stack {
            display: flex;
            height: 120px;
            margin: 30px 0;
            position: relative;
        }
        .layer {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;
        }
        .layer-name {
            font-weight: bold;
            margin-bottom: 8px;
        }
        .layer-value {
            font-size: 0.9rem;
            color: rgba(255,255,255,0.7);
            text-align: center;
        }
        .active-layer {
            background: rgba(56, 189, 248, 0.15);
            border: 1px solid rgba(56, 189, 248, 0.5);
            border-radius: 8px;
            transform: scale(1.05);
            box-shadow: 0 0 15px rgba(56, 189, 248, 0.3);
        }
        .connector {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            top: 50%;
            left: 0;
            right: 0;
            z-index: 1;
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .essentials-diagram {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 380px;
            position: relative;
            margin: 30px 0;
        }
        .core-circle {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            background: rgba(56, 189, 248, 0.15);
            border: 2px solid rgba(56, 189, 248, 0.5);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 2;
            box-shadow: 0 0 30px rgba(56, 189, 248, 0.3);
        }
        .element {
            position: absolute;
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .element:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .connector-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(56, 189, 248, 0.5), transparent);
            transform-origin: left center;
            z-index: 1;
        }
        .quote-box {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-left: 4px solid #a78bfa;
            border-radius: 0 8px 8px 0;
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .chart-container {
            position: relative;
            height: 350px;
            width: 100%;
        }
        .data-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .phase-arrow {
            position: relative;
            height: 60px;
            display: flex;
            align-items: center;
        }
        .phase-arrow::after {
            content: "";
            position: absolute;
            right: -20px;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            border-left: 15px solid #38bdf8;
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .tech-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .tech-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .icon-wrapper {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
        }
        .voice-icon {
            background: linear-gradient(135deg, #a78bfa 0%, #7c3aed 100%);
        }
        .coding-icon {
            background: linear-gradient(135deg, #38bdf8 0%, #0284c7 100%);
        }
        .quote-mark {
            font-size: 4rem;
            line-height: 1;
            color: rgba(255, 255, 255, 0.1);
            position: absolute;
            top: -10px;
            left: 10px;
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .agent-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .sector-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            flex-shrink: 0;
        }
        .quote-bubble {
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 18px;
            padding: 12px 18px;
        }
        .quote-bubble::after {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 20px;
            border-width: 10px 10px 0;
            border-style: solid;
            border-color: rgba(255, 255, 255, 0.1) transparent transparent;
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .challenge-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .challenge-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .challenge-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-weight: bold;
            flex-shrink: 0;
        }
        .analogy-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .mindset-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .mindset-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .mindset-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 10px;
        }
        .comparison-table th {
            text-align: left;
            padding: 12px 16px;
            font-weight: 600;
        }
        .comparison-table td {
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .comparison-table td:first-child {
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px 0 0 8px;
        }
        .comparison-table td:last-child {
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0 8px 8px 0;
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .future-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .future-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: "";
            position: absolute;
            left: 7px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #38bdf8, #a78bfa);
        }
        .timeline-item {
            position: relative;
            padding-bottom: 20px;
        }
        .timeline-item::before {
            content: "";
            position: absolute;
            left: -30px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #38bdf8;
        }
        .quote-bubble {
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 18px;
            padding: 12px 18px;
            margin-top: 10px;
        }
        .quote-bubble::after {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 20px;
            border-width: 10px 10px 0;
            border-style: solid;
            border-color: rgba(255, 255, 255, 0.1) transparent transparent;
        }
    

        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Montserrat', sans-serif;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 80px;
            box-sizing: border-box;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .action-button {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 30px;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(56, 189, 248, 0.4);
        }
    

        /* 强制覆盖容器背景色 - 必须在原始样式之后 */
        body, html {
            background: white !important;
        }
        .presentation-container {
            background: white !important;
        }
        .slide-page {
            background: white !important;
        }

        /* 确保图表和canvas元素可见 */
        canvas { display: block !important; width: 100% !important; height: auto !important; min-height: 200px !important; visibility: visible !important; }
        .chart-container { display: block !important; width: 100% !important; height: 450px !important; min-height: 300px !important; visibility: visible !important; }
        .slide { display: block !important; width: 100% !important; min-height: 600px !important; overflow: visible !important; }
    </style>
</head>
<body>
    <div class="navigation">
        <button class="nav-button" onclick="window.print()">打印/保存为PDF</button>
        <button class="nav-button" onclick="scrollToTop()">回到顶部</button>
        <button class="nav-button" onclick="toggleFullscreen()">全屏模式</button>
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / 13
    </div>

    <div class="slide-navigation">
        <button class="slide-nav-button" onclick="previousSlide()" id="prev-btn">上一页</button>
        <span id="slide-indicator">第 1 页</span>
        <button class="slide-nav-button" onclick="nextSlide()" id="next-btn">下一页</button>
    </div>

    <div class="presentation-container" id="presentation">
        
      <div class="slide-page" data-slide="1" id="slide-1">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="content">
            <h1 class="text-6xl font-bold mb-6 gradient-text">掘金AI的万亿美元机会</h1>
            <h2 class="text-2xl text-gray-300 mb-12">红杉资本最新内部分享</h2>
            
            <div class="flex items-center justify-center space-x-8 mb-16">
                <div class="text-left">
                    <p class="text-gray-400 mb-1">演讲者</p>
                    <p class="text-xl">Pat Grady, Sonya Huang, Konstantine Buhler</p>
                </div>
                <div class="h-12 w-px bg-gray-600"></div>
                <div class="text-left">
                    <p class="text-gray-400 mb-1">来源</p>
                    <p class="text-xl">AI Ascent 2024</p>
                </div>
            </div>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-calendar-alt mr-2"></i>2024年
        </div>
        
        <div class="absolute bottom-8 left-8">
            <div class="flex items-center">
                <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center mr-3">
                    <i class="fas fa-robot text-white"></i>
                </div>
                <span class="font-medium">AI战略洞察</span>
            </div>
        </div>
    </div>



      </div>

      <div class="slide-page" data-slide="2" id="slide-2">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">内容目录</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
        </div>
        
        <div class="grid grid-cols-2 gap-8">
            <div class="space-y-6">
                <div class="menu-item pl-4 py-2">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-4">
                            <span class="font-bold">1</span>
                        </div>
                        <h3 class="text-xl">市场机遇分析</h3>
                    </div>
                    <p class="text-gray-400 ml-12 mt-1">AI的万亿级市场潜力</p>
                </div>
                
                <div class="menu-item pl-4 py-2">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center mr-4">
                            <span class="font-bold">2</span>
                        </div>
                        <h3 class="text-xl">应用层价值高地</h3>
                    </div>
                    <p class="text-gray-400 ml-12 mt-1">AI创业的成功要素</p>
                </div>
                
                <div class="menu-item pl-4 py-2">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-pink-500 flex items-center justify-center mr-4">
                            <span class="font-bold">3</span>
                        </div>
                        <h3 class="text-xl">技术突破领域</h3>
                    </div>
                    <p class="text-gray-400 ml-12 mt-1">语音与编程的进展</p>
                </div>
            </div>
            
            <div class="space-y-6">
                <div class="menu-item pl-4 py-2">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center mr-4">
                            <span class="font-bold">4</span>
                        </div>
                        <h3 class="text-xl">垂直智能体机会</h3>
                    </div>
                    <p class="text-gray-400 ml-12 mt-1">专业领域的AI代理</p>
                </div>
                
                <div class="menu-item pl-4 py-2">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-yellow-500 flex items-center justify-center mr-4">
                            <span class="font-bold">5</span>
                        </div>
                        <h3 class="text-xl">智能体经济</h3>
                    </div>
                    <p class="text-gray-400 ml-12 mt-1">三大技术挑战</p>
                </div>
                
                <div class="menu-item pl-4 py-2">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-indigo-500 flex items-center justify-center mr-4">
                            <span class="font-bold">6</span>
                        </div>
                        <h3 class="text-xl">未来展望</h3>
                    </div>
                    <p class="text-gray-400 ml-12 mt-1">AI将如何重塑经济</p>
                </div>
            </div>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-list-ol mr-2"></i>目录
        </div>
    </div>



      </div>

      <div class="slide-page" data-slide="3" id="slide-3">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">市场机遇：万亿级AI冲击波</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
        </div>
        
        <div class="grid grid-cols-12 gap-6">
            <div class="col-span-7">
                <div class="card p-8 h-full">
                    <h3 class="text-2xl font-bold mb-6 flex items-center">
                        <i class="fas fa-chart-line mr-3 text-blue-400"></i>
                        <span>市场规模对比</span>
                    </h3>
                    
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="highlight-number mr-4">10X</div>
                            <div>
                                <h4 class="text-lg font-semibold">AI市场潜力是云计算的10倍</h4>
                                <p class="text-gray-400 mt-2">云计算目前是4000亿美元产业，AI起点市场预计大一个数量级</p>
                            </div>
                        </div>
                        
                        <div class="border-t border-gray-700 pt-6">
                            <div class="flex">
                                <div class="w-1/2 pr-4">
                                    <h4 class="font-semibold text-blue-400 mb-2">云计算转型</h4>
                                    <p class="text-sm text-gray-400">从软件市场起步，现已成长为4000亿美元产业</p>
                                </div>
                                <div class="w-1/2 pl-4 border-l border-gray-700">
                                    <h4 class="font-semibold text-purple-400 mb-2">AI转型</h4>
                                    <p class="text-sm text-gray-400">同时冲击软件市场和服务市场，潜力更为巨大</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-span-5">
                <div class="card p-8 h-full">
                    <h3 class="text-2xl font-bold mb-6 flex items-center">
                        <i class="fas fa-bolt mr-3 text-yellow-400"></i>
                        <span>技术普及速度</span>
                    </h3>
                    
                    <ul class="space-y-4">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mt-1 mr-3"></i>
                            <span>ChatGPT发布后立即获得全球关注</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mt-1 mr-3"></i>
                            <span>Reddit和Twitter(X)每月12-18亿活跃用户</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mt-1 mr-3"></i>
                            <span>全球互联网用户从2亿增长到56亿</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mt-1 mr-3"></i>
                            <span>"轨道已经铺设完毕" - 技术分发的新现实</span>
                        </li>
                    </ul>
                    
                    <div class="mt-8 p-4 bg-gray-800 rounded-lg">
                        <p class="italic text-gray-300">"大自然厌恶真空。市场上对AI存在着巨大的'吸力'，所有宏观经济的噪音都无关紧要。"</p>
                        <p class="text-right mt-2 text-blue-400">— Pat Grady</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-lightbulb mr-2"></i>红杉资本AI Ascent 2024
        </div>
    </div>



      </div>

      <div class="slide-page" data-slide="4" id="slide-4">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">AI普及速度对比</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
            <p class="text-gray-400 mt-2">技术分发的新现实：物理规则已经改变</p>
        </div>
        
        <div class="grid grid-cols-12 gap-6 mb-8">
            <div class="col-span-8">
                <div class="chart-container">
                    <canvas id="adoptionChart"></canvas>
                </div>
            </div>
            <div class="col-span-4">
                <div class="data-card p-6 h-full">
                    <h3 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fas fa-rocket mr-3 text-purple-400"></i>
                        <span>关键里程碑</span>
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="highlight-box pl-4 py-2">
                            <p class="font-semibold">2004年</p>
                            <p class="text-sm text-gray-400">Salesforce艰难推广云计算概念</p>
                        </div>
                        <div class="highlight-box pl-4 py-2">
                            <p class="font-semibold">2022年11月30日</p>
                            <p class="text-sm text-gray-400">ChatGPT发布，立即获得全球关注</p>
                        </div>
                        <div class="highlight-box pl-4 py-2">
                            <p class="font-semibold">12亿-18亿</p>
                            <p class="text-sm text-gray-400">Reddit和Twitter(X)月活跃用户</p>
                        </div>
                        <div class="highlight-box pl-4 py-2">
                            <p class="font-semibold">2亿→56亿</p>
                            <p class="text-sm text-gray-400">全球互联网用户增长</p>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-gray-800 rounded-lg">
                        <p class="text-sm italic text-gray-300">"与云计算刚起步时相比，AI的普及速度惊人...轨道已经铺设完毕"</p>
                        <p class="text-right mt-1 text-sm text-blue-400">— Pat Grady</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-3 gap-4">
            <div class="data-card p-4">
                <div class="flex items-center mb-2">
                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-3">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <h4 class="font-bold">用户基础</h4>
                </div>
                <p class="text-sm text-gray-400">全球互联网用户覆盖几乎所有家庭和企业</p>
            </div>
            <div class="data-card p-4">
                <div class="flex items-center mb-2">
                    <div class="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                        <i class="fas fa-share-alt text-white"></i>
                    </div>
                    <h4 class="font-bold">传播渠道</h4>
                </div>
                <p class="text-sm text-gray-400">社交媒体平台提供即时信息分享能力</p>
            </div>
            <div class="data-card p-4">
                <div class="flex items-center mb-2">
                    <div class="w-8 h-8 rounded-full bg-pink-500 flex items-center justify-center mr-3">
                        <i class="fas fa-bolt text-white"></i>
                    </div>
                    <h4 class="font-bold">基础设施</h4>
                </div>
                <p class="text-sm text-gray-400">云计算和移动设备已普及，技术分发无障碍</p>
            </div>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-chart-bar mr-2"></i>数据来源：红杉资本分析
        </div>

        <script>
            const ctx = document.getElementById('adoptionChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['云计算(2004)', 'AI(2022)'],
                    datasets: [{
                        label: '达到1亿用户所需时间(天)',
                        data: [1500, 60],
                        backgroundColor: [
                            'rgba(56, 189, 248, 0.7)',
                            'rgba(167, 139, 250, 0.7)'
                        ],
                        borderColor: [
                            'rgba(56, 189, 248, 1)',
                            'rgba(167, 139, 250, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: 'white',
                                font: {
                                    family: 'Montserrat',
                                    size: 14
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.parsed.y + ' 天';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)',
                                callback: function(value) {
                                    return value + '天';
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)',
                                font: {
                                    family: 'Montserrat',
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        </script>
    </div>



      </div>

      <div class="slide-page" data-slide="5" id="slide-5">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">应用层价值高地</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
            <p class="text-gray-400 mt-2">红杉资本：95%的AI公司成功要素与传统公司相同</p>
        </div>
        
        <div class="tech-stack">
            <div class="connector"></div>
            <div class="layer">
                <div class="layer-name">基础设施</div>
                <div class="layer-value">芯片/云计算</div>
            </div>
            <div class="layer">
                <div class="layer-name">模型层</div>
                <div class="layer-value">大语言模型</div>
            </div>
            <div class="layer active-layer">
                <div class="layer-name">应用层</div>
                <div class="layer-value">价值汇聚地</div>
            </div>
        </div>
        
        <div class="grid grid-cols-3 gap-6 mt-8">
            <div class="feature-card p-6">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center mr-4">
                        <i class="fas fa-user-tie text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold">垂直领域深耕</h3>
                </div>
                <p class="text-gray-400">从客户需求出发，专注于特定垂直领域，解决需要人在环中的复杂问题</p>
            </div>
            
            <div class="feature-card p-6">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center mr-4">
                        <i class="fas fa-shield-alt text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold">警惕"氛围营收"</h3>
                </div>
                <p class="text-gray-400">检查用户采用率、参与度和留存率，区分真实营收与暂时性热度</p>
            </div>
            
            <div class="feature-card p-6">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 rounded-full bg-pink-500 flex items-center justify-center mr-4">
                        <i class="fas fa-database text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold">数据飞轮</h3>
                </div>
                <p class="text-gray-400">构建与业务指标挂钩的数据飞轮，这是最强大的护城河之一</p>
            </div>
        </div>
        
        <div class="mt-8 grid grid-cols-2 gap-6">
            <div class="bg-gray-800 rounded-lg p-6 border-l-4 border-blue-500">
                <h3 class="text-xl font-bold mb-3 flex items-center">
                    <i class="fas fa-arrow-up mr-3 text-blue-400"></i>
                    <span>价值链上移</span>
                </h3>
                <p class="text-gray-400">从销售工具转向销售成果，从争夺软件预算转向抢占人力资源预算</p>
            </div>
            
            <div class="bg-gray-800 rounded-lg p-6 border-l-4 border-purple-500">
                <h3 class="text-xl font-bold mb-3 flex items-center">
                    <i class="fas fa-running mr-3 text-purple-400"></i>
                    <span>全力以赴</span>
                </h3>
                <p class="text-gray-400">"市场上有一个巨大的吸力，如果你不抢占先机，别人就会。现在是全力以赴的时候。"</p>
            </div>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-chess-queen mr-2"></i>AI创业制胜关键
        </div>
    </div>



      </div>

      <div class="slide-page" data-slide="6" id="slide-6">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">构建AI公司的关键要素</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
            <p class="text-gray-400 mt-2">红杉资本Pat Grady：95%与传统公司相同，但有5%的AI特有要素</p>
        </div>
        
        <div class="essentials-diagram">
            <div class="core-circle">
                <div class="text-xl font-bold mb-1">AI公司</div>
                <div class="text-sm text-gray-400">95%通用要素</div>
            </div>
            
            <div class="element" style="top: 20%; left: 15%;">
                <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center mb-2">
                    <i class="fas fa-filter text-white"></i>
                </div>
                <div class="font-semibold">警惕"氛围营收"</div>
                <div class="text-xs text-gray-400 mt-1">检查真实采用率与留存</div>
            </div>
            
            <div class="element" style="top: 20%; right: 15%;">
                <div class="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center mb-2">
                    <i class="fas fa-percentage text-white"></i>
                </div>
                <div class="font-semibold">毛利率路径</div>
                <div class="text-xs text-gray-400 mt-1">清晰的盈利路线图</div>
            </div>
            
            <div class="element" style="bottom: 20%; left: 15%;">
                <div class="w-10 h-10 rounded-full bg-pink-500 flex items-center justify-center mb-2">
                    <i class="fas fa-cogs text-white"></i>
                </div>
                <div class="font-semibold">数据飞轮</div>
                <div class="text-xs text-gray-400 mt-1">与业务指标挂钩</div>
            </div>
            
            <div class="element" style="bottom: 20%; right: 15%;">
                <div class="w-10 h-10 rounded-full bg-green-500 flex items-center justify-center mb-2">
                    <i class="fas fa-handshake text-white"></i>
                </div>
                <div class="font-semibold">用户信任</div>
                <div class="text-xs text-gray-400 mt-1">比产品更重要</div>
            </div>
            
            <!-- Connector lines -->
            <div class="connector-line" style="width: 150px; left: 180px; top: 90px; transform: rotate(-45deg);"></div>
            <div class="connector-line" style="width: 150px; right: 180px; top: 90px; transform: rotate(45deg);"></div>
            <div class="connector-line" style="width: 150px; left: 180px; bottom: 90px; transform: rotate(45deg);"></div>
            <div class="connector-line" style="width: 150px; right: 180px; bottom: 90px; transform: rotate(-45deg);"></div>
        </div>
        
        <div class="quote-box p-4 mt-6">
            <p class="italic text-gray-300">"在当前发展阶段，信任比你的产品更重要。产品会逐步变得更好，如果客户信任你能把它做得更好，你就没问题；如果他们不信任你，你就麻烦了。"</p>
            <p class="text-right mt-2 text-blue-400">— Pat Grady</p>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-key mr-2"></i>成功要素
        </div>
    </div>



      </div>

      <div class="slide-page" data-slide="7" id="slide-7">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">从炒作到真实价值</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
            <p class="text-gray-400 mt-2">Sonya Huang：AI用户参与度的戏剧性转变</p>
        </div>
        
        <div class="grid grid-cols-12 gap-6 mb-8">
            <div class="col-span-8">
                <div class="chart-container">
                    <canvas id="engagementChart"></canvas>
                </div>
            </div>
            <div class="col-span-4">
                <div class="data-card p-6 h-full">
                    <h3 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fas fa-exchange-alt mr-3 text-purple-400"></i>
                        <span>关键转变</span>
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="phase-arrow pr-6">
                            <div>
                                <p class="font-semibold">2023年初</p>
                                <p class="text-sm text-gray-400">DAU/MAU比率低，炒作为主</p>
                            </div>
                        </div>
                        <div class="phase-arrow pr-6">
                            <div>
                                <p class="font-semibold">2024年</p>
                                <p class="text-sm text-gray-400">DAU/MAU接近Reddit水平</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-8">
                        <h4 class="font-semibold mb-2">真实应用案例</h4>
                        <ul class="space-y-3 text-sm">
                            <li class="flex items-start">
                                <i class="fas fa-ad text-blue-400 mt-1 mr-2"></i>
                                <span>广告领域：精准美观的广告文案创作</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-graduation-cap text-green-400 mt-1 mr-2"></i>
                                <span>教育领域：新概念的即时可视化</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-heartbeat text-pink-400 mt-1 mr-2"></i>
                                <span>医疗健康：辅助诊断(如OpenEvidence)</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-3 gap-4">
            <div class="data-card p-4">
                <div class="flex items-center mb-2">
                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-3">
                        <i class="fas fa-fire text-white"></i>
                    </div>
                    <h4 class="font-bold">早期阶段</h4>
                </div>
                <p class="text-sm text-gray-400">用户因好奇尝试，但使用频率低</p>
            </div>
            <div class="data-card p-4">
                <div class="flex items-center mb-2">
                    <div class="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                    <h4 class="font-bold">转折点</h4>
                </div>
                <p class="text-sm text-gray-400">DAU/MAU比率显著提升</p>
            </div>
            <div class="data-card p-4">
                <div class="flex items-center mb-2">
                    <div class="w-8 h-8 rounded-full bg-pink-500 flex items-center justify-center mr-3">
                        <i class="fas fa-check-circle text-white"></i>
                    </div>
                    <h4 class="font-bold">当前阶段</h4>
                </div>
                <p class="text-sm text-gray-400">用户从AI中获得真实价值</p>
            </div>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-user-check mr-2"></i>用户参与度分析
        </div>

        <script>
            const ctx = document.getElementById('engagementChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['2023 Q1', '2023 Q2', '2023 Q3', '2023 Q4', '2024 Q1'],
                    datasets: [{
                        label: 'AI应用DAU/MAU比率(%)',
                        data: [15, 22, 35, 48, 62],
                        fill: true,
                        backgroundColor: 'rgba(56, 189, 248, 0.2)',
                        borderColor: 'rgba(56, 189, 248, 1)',
                        tension: 0.3,
                        pointBackgroundColor: 'white',
                        pointBorderColor: '#38bdf8',
                        pointBorderWidth: 2
                    },
                    {
                        label: 'Reddit DAU/MAU(%)',
                        data: [60, 60, 60, 60, 60],
                        borderColor: 'rgba(167, 139, 250, 0.7)',
                        borderDash: [5, 5],
                        borderWidth: 1,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: 'white',
                                font: {
                                    family: 'Montserrat',
                                    size: 14
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + '%';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)',
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)',
                                font: {
                                    family: 'Montserrat',
                                    size: 14
                                }
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        </script>
    </div>



      </div>

      <div class="slide-page" data-slide="8" id="slide-8">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">2024年AI两大突破领域</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
            <p class="text-gray-400 mt-2">Sonya Huang：语音生成与编程领域达到关键里程碑</p>
        </div>
        
        <div class="grid grid-cols-2 gap-8 mb-8">
            <div class="tech-card p-8">
                <div class="icon-wrapper voice-icon">
                    <i class="fas fa-microphone-alt text-white text-2xl"></i>
                </div>
                <h2 class="text-2xl font-bold mb-4">语音生成技术</h2>
                <p class="text-gray-400 mb-4">完全跨越"恐怖谷"，达到接近以假乱真的水平</p>
                
                <div class="relative bg-gray-800 rounded-lg p-6 mt-6">
                    <div class="quote-mark">"</div>
                    <p class="italic text-gray-300 relative z-10">语音领域的"《她》时刻"已经到来，科幻与现实之间的鸿沟正在以惊人速度弥合。</p>
                    <p class="text-right mt-2 text-purple-400">— Sonya Huang</p>
                </div>
                
                <ul class="mt-6 space-y-3">
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-purple-400 mt-1 mr-3"></i>
                        <span>自然度达到人类水平</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-purple-400 mt-1 mr-3"></i>
                        <span>情感表达丰富</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-purple-400 mt-1 mr-3"></i>
                        <span>实时生成能力</span>
                    </li>
                </ul>
            </div>
            
            <div class="tech-card p-8">
                <div class="icon-wrapper coding-icon">
                    <i class="fas fa-code text-white text-2xl"></i>
                </div>
                <h2 class="text-2xl font-bold mb-4">编程领域</h2>
                <p class="text-gray-400 mb-4">达到"尖叫级"产品市场契合度</p>
                
                <div class="relative bg-gray-800 rounded-lg p-6 mt-6">
                    <div class="quote-mark">"</div>
                    <p class="italic text-gray-300 relative z-10">无论你是"十倍效能"工程师还是编程新手，AI都在根本上改变软件创造的可及性。</p>
                    <p class="text-right mt-2 text-blue-400">— Sonya Huang</p>
                </div>
                
                <ul class="mt-6 space-y-3">
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-blue-400 mt-1 mr-3"></i>
                        <span>Claude 3.5 Sonnet发布后发生"氛围转变"</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-blue-400 mt-1 mr-3"></i>
                        <span>非专业开发者也能创建复杂应用</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-blue-400 mt-1 mr-3"></i>
                        <span>开发速度与经济效益大幅提升</span>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="bg-gray-800 rounded-lg p-6 border-l-4 border-green-500">
            <h3 class="text-xl font-bold mb-3 flex items-center">
                <i class="fas fa-lightbulb mr-3 text-green-400"></i>
                <span>行业影响</span>
            </h3>
            <p class="text-gray-400">"vibe coding"现象兴起，有人已用AI制作出DocSend的替代品，预示着软件开发民主化的新时代</p>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-bolt mr-2"></i>技术突破
        </div>
    </div>



      </div>

      <div class="slide-page" data-slide="9" id="slide-9">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">垂直智能体机会</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
            <p class="text-gray-400 mt-2">Sonya Huang：端到端训练的AI系统在特定任务上超越人类专家</p>
        </div>
        
        <div class="grid grid-cols-3 gap-6 mb-8">
            <div class="agent-card p-6">
                <div class="flex items-center mb-4">
                    <div class="sector-icon bg-blue-500">
                        <i class="fas fa-shield-alt text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold">安全领域</h3>
                </div>
                <div class="quote-bubble mb-4">
                    <p class="text-sm">Expo公司的智能体已超越人类渗透测试员</p>
                </div>
                <p class="text-gray-400 text-sm">通过合成数据和强化学习训练，在漏洞发现和系统渗透方面表现卓越</p>
            </div>
            
            <div class="agent-card p-6">
                <div class="flex items-center mb-4">
                    <div class="sector-icon bg-purple-500">
                        <i class="fas fa-server text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold">DevOps</h3>
                </div>
                <div class="quote-bubble mb-4">
                    <p class="text-sm">Traversal创建了比最优秀人类更出色的AI故障排除员</p>
                </div>
                <p class="text-gray-400 text-sm">系统诊断和问题解决速度比人类工程师快3-5倍</p>
            </div>
            
            <div class="agent-card p-6">
                <div class="flex items-center mb-4">
                    <div class="sector-icon bg-pink-500">
                        <i class="fas fa-network-wired text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold">网络工程</h3>
                </div>
                <div class="quote-bubble mb-4">
                    <p class="text-sm">Meter公司的AI超越网络工程师</p>
                </div>
                <p class="text-gray-400 text-sm">在网络配置优化和故障预测方面达到专家级水平</p>
            </div>
        </div>
        
        <div class="grid grid-cols-2 gap-6">
            <div class="bg-gray-800 rounded-lg p-6 border-l-4 border-green-500">
                <h3 class="text-xl font-bold mb-3 flex items-center">
                    <i class="fas fa-brain mr-3 text-green-400"></i>
                    <span>训练方法</span>
                </h3>
                <ul class="space-y-2 text-gray-400">
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-400 mt-1 mr-2"></i>
                        <span>针对特定工作流程端到端训练</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-400 mt-1 mr-2"></i>
                        <span>结合合成数据和用户数据的强化学习</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-400 mt-1 mr-2"></i>
                        <span>在非常具体的任务上表现出色</span>
                    </li>
                </ul>
            </div>
            
            <div class="bg-gray-800 rounded-lg p-6 border-l-4 border-yellow-500">
                <h3 class="text-xl font-bold mb-3 flex items-center">
                    <i class="fas fa-hourglass-half mr-3 text-yellow-400"></i>
                    <span>富足时代挑战</span>
                </h3>
                <ul class="space-y-2 text-gray-400">
                    <li class="flex items-start">
                        <i class="fas fa-question-circle text-yellow-400 mt-1 mr-2"></i>
                        <span>当劳动力变得廉价充足，会否出现大量低质量内容？</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-question-circle text-yellow-400 mt-1 mr-2"></i>
                        <span>当"品味"成为稀缺资产时会发生什么？</span>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-robot mr-2"></i>垂直智能体
        </div>
    </div>



      </div>

      <div class="slide-page" data-slide="10" id="slide-10">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">智能体经济三大挑战</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
            <p class="text-gray-400 mt-2">Konstantine Buhler：实现智能体经济愿景必须解决的关键问题</p>
        </div>
        
        <div class="grid grid-cols-3 gap-6 mb-8">
            <div class="challenge-card p-6">
                <div class="flex items-center mb-4">
                    <div class="challenge-number bg-blue-500">1</div>
                    <h3 class="text-xl font-bold">持久身份</h3>
                </div>
                <p class="text-gray-400 mb-4">智能体需要保持一致性，同时记住并理解用户</p>
                
                <div class="analogy-box">
                    <p class="text-sm italic text-gray-300">"如果你和一个做生意的人打交道，而他每天都在变，你很可能不会长期合作下去"</p>
                </div>
                
                <ul class="mt-4 space-y-2 text-sm">
                    <li class="flex items-start">
                        <i class="fas fa-database text-blue-400 mt-1 mr-2"></i>
                        <span>当前解决方案：RAG、向量数据库、长上下文窗口</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-blue-400 mt-1 mr-2"></i>
                        <span>挑战：实现真正的记忆和基于记忆的自我学习</span>
                    </li>
                </ul>
            </div>
            
            <div class="challenge-card p-6">
                <div class="flex items-center mb-4">
                    <div class="challenge-number bg-purple-500">2</div>
                    <h3 class="text-xl font-bold">通信协议</h3>
                </div>
                <p class="text-gray-400 mb-4">智能体之间需要标准化的交互方式</p>
                
                <div class="analogy-box">
                    <p class="text-sm italic text-gray-300">"想象一下没有TCP/IP和互联网的个人计算会是怎样"</p>
                </div>
                
                <ul class="mt-4 space-y-2 text-sm">
                    <li class="flex items-start">
                        <i class="fas fa-project-diagram text-purple-400 mt-1 mr-2"></i>
                        <span>正在发展：MCP(模型协作协议)</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-exchange-alt text-purple-400 mt-1 mr-2"></i>
                        <span>需要：信息传递、价值传递和信任传递的协议</span>
                    </li>
                </ul>
            </div>
            
            <div class="challenge-card p-6">
                <div class="flex items-center mb-4">
                    <div class="challenge-number bg-pink-500">3</div>
                    <h3 class="text-xl font-bold">安全与信任</h3>
                </div>
                <p class="text-gray-400 mb-4">无法面对面交流时，安全和信任更为关键</p>
                
                <div class="analogy-box">
                    <p class="text-sm italic text-gray-300">"这将催生一个围绕信任和安全的完整产业"</p>
                </div>
                
                <ul class="mt-4 space-y-2 text-sm">
                    <li class="flex items-start">
                        <i class="fas fa-shield-alt text-pink-400 mt-1 mr-2"></i>
                        <span>比当前经济中更为重要</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-industry text-pink-400 mt-1 mr-2"></i>
                        <span>需要建立新的验证和认证机制</span>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="bg-gray-800 rounded-lg p-6 border-l-4 border-yellow-500">
            <h3 class="text-xl font-bold mb-3 flex items-center">
                <i class="fas fa-brain mr-3 text-yellow-400"></i>
                <span>思维方式的根本转变</span>
            </h3>
            <div class="grid grid-cols-3 gap-4">
                <div>
                    <h4 class="font-semibold text-blue-400 mb-2">随机性思维</h4>
                    <p class="text-gray-400 text-sm">从确定性计算转向接受AI的随机性输出</p>
                </div>
                <div>
                    <h4 class="font-semibold text-purple-400 mb-2">管理思维</h4>
                    <p class="text-gray-400 text-sm">理解智能体的能力和局限，做出管理决策</p>
                </div>
                <div>
                    <h4 class="font-semibold text-pink-400 mb-2">杠杆效应</h4>
                    <p class="text-gray-400 text-sm">更强的能力伴随更大的不确定性</p>
                </div>
            </div>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-puzzle-piece mr-2"></i>技术挑战
        </div>
    </div>



      </div>

      <div class="slide-page" data-slide="11" id="slide-11">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">思维方式的根本转变</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
            <p class="text-gray-400 mt-2">Konstantine Buhler：智能体经济将重塑我们的思考和工作方式</p>
        </div>
        
        <div class="grid grid-cols-3 gap-6 mb-8">
            <div class="mindset-card p-6 flex flex-col items-center text-center">
                <div class="mindset-icon bg-blue-500">
                    <i class="fas fa-dice text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-3">随机性思维</h3>
                <p class="text-gray-400 mb-4">从确定性计算转向接受AI的随机性输出</p>
                <div class="bg-gray-800 rounded-lg p-4 w-full">
                    <p class="text-sm italic text-gray-300">"如果让计算机记住数字73，它明天、下周、下个月都会记住。但让AI记住，它可能记住73，也可能记住37、72、74，或者下一个质数79"</p>
                </div>
            </div>
            
            <div class="mindset-card p-6 flex flex-col items-center text-center">
                <div class="mindset-icon bg-purple-500">
                    <i class="fas fa-users-cog text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-3">管理思维</h3>
                <p class="text-gray-400 mb-4">从执行者转变为智能体管理者</p>
                <div class="bg-gray-800 rounded-lg p-4 w-full">
                    <p class="text-sm italic text-gray-300">"类似于从独立贡献者转变为管理者的过程，需要做出更复杂的管理决策"</p>
                </div>
            </div>
            
            <div class="mindset-card p-6 flex flex-col items-center text-center">
                <div class="mindset-icon bg-pink-500">
                    <i class="fas fa-balance-scale-right text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-3">杠杆效应</h3>
                <p class="text-gray-400 mb-4">能力增强伴随确定性降低</p>
                <div class="bg-gray-800 rounded-lg p-4 w-full">
                    <p class="text-sm italic text-gray-300">"你可以做更多事情，但必须能够管理这种不确定性和风险"</p>
                </div>
            </div>
        </div>
        
        <div class="bg-gray-800 rounded-lg p-6">
            <h3 class="text-xl font-bold mb-4 flex items-center">
                <i class="fas fa-not-equal mr-3 text-yellow-400"></i>
                <span>传统思维 vs 智能体思维</span>
            </h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>维度</th>
                        <th>传统计算思维</th>
                        <th>智能体经济思维</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>确定性</td>
                        <td>输入→确定性输出</td>
                        <td>输入→概率性输出</td>
                    </tr>
                    <tr>
                        <td>控制方式</td>
                        <td>精确控制每个步骤</td>
                        <td>设定目标，管理过程</td>
                    </tr>
                    <tr>
                        <td>错误处理</td>
                        <td>错误可完全复现</td>
                        <td>错误可能具有随机性</td>
                    </tr>
                    <tr>
                        <td>工作角色</td>
                        <td>执行者</td>
                        <td>管理者+协调者</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-brain mr-2"></i>认知变革
        </div>
    </div>



      </div>

      <div class="slide-page" data-slide="12" id="slide-12">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-12">
            <h1 class="text-4xl font-bold mb-2 gradient-text">杠杆效应与未来展望</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
            <p class="text-gray-400 mt-2">Konstantine Buhler：前所未有的杠杆水平将重塑经济结构</p>
        </div>
        
        <div class="grid grid-cols-12 gap-6 mb-8">
            <div class="col-span-5">
                <div class="future-card p-6 h-full">
                    <h3 class="text-2xl font-bold mb-4 flex items-center">
                        <i class="fas fa-chess-queen mr-3 text-blue-400"></i>
                        <span>杠杆效应演进</span>
                    </h3>
                    
                    <div class="timeline">
                        <div class="timeline-item">
                            <h4 class="font-bold text-blue-400">2023年预测</h4>
                            <p class="text-gray-400 text-sm">组织中各职能部门开始拥有AI智能体</p>
                        </div>
                        <div class="timeline-item">
                            <h4 class="font-bold text-purple-400">2024年现状</h4>
                            <p class="text-gray-400 text-sm">公司以前所未有的速度扩张，用更少的人</p>
                        </div>
                        <div class="timeline-item">
                            <h4 class="font-bold text-pink-400">未来展望</h4>
                            <p class="text-gray-400 text-sm">"一人独角兽公司"可能出现</p>
                        </div>
                    </div>
                    
                    <div class="quote-bubble mt-6">
                        <p class="italic text-gray-300">"最终，这些流程和智能体将会融合，形成一个神经网络的网络"</p>
                        <p class="text-right mt-2 text-blue-400">— Konstantine Buhler</p>
                    </div>
                </div>
            </div>
            
            <div class="col-span-7">
                <div class="future-card p-6 h-full">
                    <h3 class="text-2xl font-bold mb-4 flex items-center">
                        <i class="fas fa-project-diagram mr-3 text-purple-400"></i>
                        <span>经济重构的三个层面</span>
                    </h3>
                    
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-gray-800 rounded-lg p-4 border-l-4 border-blue-500">
                            <h4 class="font-bold text-blue-400 mb-2">个体工作</h4>
                            <ul class="text-sm text-gray-400 space-y-2">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-blue-400 mt-1 mr-2"></i>
                                    <span>从执行者转变为管理者</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-blue-400 mt-1 mr-2"></i>
                                    <span>专注高价值决策</span>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="bg-gray-800 rounded-lg p-4 border-l-4 border-purple-500">
                            <h4 class="font-bold text-purple-400 mb-2">公司结构</h4>
                            <ul class="text-sm text-gray-400 space-y-2">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-purple-400 mt-1 mr-2"></i>
                                    <span>扁平化组织架构</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-purple-400 mt-1 mr-2"></i>
                                    <span>智能体网络协作</span>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="bg-gray-800 rounded-lg p-4 border-l-4 border-pink-500">
                            <h4 class="font-bold text-pink-400 mb-2">整体经济</h4>
                            <ul class="text-sm text-gray-400 space-y-2">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-pink-400 mt-1 mr-2"></i>
                                    <span>智能体间交易市场</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-pink-400 mt-1 mr-2"></i>
                                    <span>新型信任机制</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6">
                        <h4 class="font-bold text-white mb-2">红杉资本的行动号召</h4>
                        <p class="text-gray-200">"现在是全力以赴，保持最大速度前进的时候。市场上有一个巨大的吸力，如果你不抢占先机，别人就会。"</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="absolute bottom-8 right-8 text-gray-500 text-sm">
            <i class="fas fa-rocket mr-2"></i>未来已来
        </div>
    </div>



      </div>

      <div class="slide-page" data-slide="13" id="slide-13">
        
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <h1 class="text-5xl font-bold mb-6 gradient-text">掘金AI的万亿美元机会</h1>
        
        <div class="max-w-2xl mx-auto">
            <p class="text-xl text-gray-300 mb-8">现在是全力以赴，保持最大速度前进的时候</p>
            
            <div class="flex justify-center space-x-8 mb-10">
                <div class="text-center">
                    <div class="w-16 h-16 rounded-full bg-blue-500 flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-lightbulb text-white text-2xl"></i>
                    </div>
                    <p class="font-medium">红杉资本AI Ascent 2024</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 rounded-full bg-purple-500 flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-chart-line text-white text-2xl"></i>
                    </div>
                    <p class="font-medium">智能体经济即将到来</p>
                </div>
            </div>
            
            <button class="action-button">
                <i class="fas fa-paper-plane mr-2"></i>获取完整报告
            </button>
        </div>
        
        <div class="absolute bottom-8 left-0 right-0 text-center text-gray-500 text-sm">
            <p>© 2024 红杉资本 | AI战略洞察 | 保密资料</p>
        </div>
    </div>



      </div>
    </div>

    <script>
        // 增强JavaScript执行函数
        function enhanceJavaScriptExecution() {
            console.log('Enhancing JavaScript execution in exported PPT...');

            // 确保所有canvas元素可见
            document.querySelectorAll('canvas').forEach(function(canvas) {
                canvas.style.display = 'block';
                canvas.style.visibility = 'visible';
                canvas.style.width = '100%';
                canvas.style.minHeight = '200px';
            });

            // 确保图表容器可见
            document.querySelectorAll('.chart-container').forEach(function(container) {
                container.style.display = 'block';
                container.style.visibility = 'visible';
                container.style.height = '450px';
                container.style.width = '100%';
                container.style.minHeight = '300px';
            });

            // 确保幻灯片元素可见
            document.querySelectorAll('.slide, [class*="slide-"]').forEach(function(slide) {
                slide.style.display = 'block';
                slide.style.overflow = 'visible';
                slide.style.height = 'auto';
                slide.style.minHeight = '600px';
                slide.style.width = '100%';
            });
        }

        // 在页面加载完成后执行增强功能
        window.addEventListener('DOMContentLoaded', function() {
            try {
                // 执行所有收集到的内联脚本
                
            const ctx = document.getElementById('adoptionChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['云计算(2004)', 'AI(2022)'],
                    datasets: [{
                        label: '达到1亿用户所需时间(天)',
                        data: [1500, 60],
                        backgroundColor: [
                            'rgba(56, 189, 248, 0.7)',
                            'rgba(167, 139, 250, 0.7)'
                        ],
                        borderColor: [
                            'rgba(56, 189, 248, 1)',
                            'rgba(167, 139, 250, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: 'white',
                                font: {
                                    family: 'Montserrat',
                                    size: 14
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.parsed.y + ' 天';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)',
                                callback: function(value) {
                                    return value + '天';
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)',
                                font: {
                                    family: 'Montserrat',
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        

            const ctx = document.getElementById('engagementChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['2023 Q1', '2023 Q2', '2023 Q3', '2023 Q4', '2024 Q1'],
                    datasets: [{
                        label: 'AI应用DAU/MAU比率(%)',
                        data: [15, 22, 35, 48, 62],
                        fill: true,
                        backgroundColor: 'rgba(56, 189, 248, 0.2)',
                        borderColor: 'rgba(56, 189, 248, 1)',
                        tension: 0.3,
                        pointBackgroundColor: 'white',
                        pointBorderColor: '#38bdf8',
                        pointBorderWidth: 2
                    },
                    {
                        label: 'Reddit DAU/MAU(%)',
                        data: [60, 60, 60, 60, 60],
                        borderColor: 'rgba(167, 139, 250, 0.7)',
                        borderDash: [5, 5],
                        borderWidth: 1,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: 'white',
                                font: {
                                    family: 'Montserrat',
                                    size: 14
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + '%';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)',
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)',
                                font: {
                                    family: 'Montserrat',
                                    size: 14
                                }
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        

                // 重新执行所有内联脚本以确保动态内容正确渲染
                document.querySelectorAll('script:not([src])').forEach(function(oldScript) {
                    try {
                        var newScript = document.createElement('script');
                        newScript.textContent = oldScript.textContent;
                        if (oldScript.parentNode) {
                            oldScript.parentNode.replaceChild(newScript, oldScript);
                        }
                    } catch (err) {
                        console.error('Error re-executing script:', err);
                    }
                });

                // 执行增强功能
                enhanceJavaScriptExecution();
            } catch (err) {
                console.error('Error in DOMContentLoaded handler:', err);
            }
        });

        let currentSlideIndex = 0;
        const totalSlides = 13;

        // 更新幻灯片指示器
        function updateSlideIndicator() {
            document.getElementById('current-slide').textContent = currentSlideIndex + 1;
            document.getElementById('slide-indicator').textContent = `第 ${currentSlideIndex + 1} 页`;

            // 更新按钮状态
            document.getElementById('prev-btn').disabled = currentSlideIndex === 0;
            document.getElementById('next-btn').disabled = currentSlideIndex === totalSlides - 1;
        }

        // 滚动到指定幻灯片
        function scrollToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                currentSlideIndex = index;
                const slide = document.getElementById(`slide-${index + 1}`);
                if (slide) {
                    slide.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
                updateSlideIndicator();
            }
        }

        // 上一页
        function previousSlide() {
            if (currentSlideIndex > 0) {
                scrollToSlide(currentSlideIndex - 1);
            }
        }

        // 下一页
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                scrollToSlide(currentSlideIndex + 1);
            }
        }

        // 回到顶部
        function scrollToTop() {
            scrollToSlide(0);
        }

        // 全屏模式
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowDown':
                case 'PageDown':
                case ' ': // 空格键
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowUp':
                case 'PageUp':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    scrollToSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    scrollToSlide(totalSlides - 1);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
            }
        });

        // 鼠标滚轮支持
        let isScrolling = false;
        document.addEventListener('wheel', function(e) {
            if (isScrolling) return;

            isScrolling = true;
            setTimeout(() => { isScrolling = false; }, 300);

            if (e.deltaY > 0) {
                // 向下滚动
                nextSlide();
            } else {
                // 向上滚动
                previousSlide();
            }
        }, { passive: true });

        // 触摸支持（移动端）
        let touchStartY = 0;
        document.addEventListener('touchstart', function(e) {
            touchStartY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', function(e) {
            const touchEndY = e.changedTouches[0].clientY;
            const diff = touchStartY - touchEndY;

            if (Math.abs(diff) > 50) { // 最小滑动距离
                if (diff > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }
        });

        // 监听滚动事件，更新当前幻灯片指示器
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                const slides = document.querySelectorAll('.slide-page');
                const viewportHeight = window.innerHeight;
                const scrollTop = window.scrollY;

                for (let i = 0; i < slides.length; i++) {
                    const slide = slides[i];
                    const rect = slide.getBoundingClientRect();

                    // 如果幻灯片在视口中心附近
                    if (rect.top <= viewportHeight / 2 && rect.bottom >= viewportHeight / 2) {
                        if (currentSlideIndex !== i) {
                            currentSlideIndex = i;
                            updateSlideIndicator();
                        }
                        break;
                    }
                }
            }, 100);
        });

        // 初始化
        updateSlideIndicator();
    </script>
</body>
</html>