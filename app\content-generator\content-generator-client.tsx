'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { extractCodeFromMessage, extractMultipleFilesFromMessage, generateDefaultFileName } from '../lib/code-extractor';
import ConversationPanel from './components/conversation-panel';
import ContentViewerPanel from './components/content-viewer-panel';
import { Conversation, GeneratedFile, FileStatus, ModelType, Task, TaskStatus, TaskExecutionPhase, FileOperation, FileOperationType, FileOperationStatus } from './types';
import AISettings from '@/components/ai-settings';
import { executeTask, generateSummary } from './task-functions';
import { promptTemplates } from './promptTemplates';
import { useAIStore } from '@/lib/ai-store';
import { useCookies } from 'next-client-cookies';

export default function ContentGeneratorClient() {
  const { model, customModels, initializeFromCookies } = useAIStore();
  const cookies = useCookies();

  // 初始化AI配置
  useEffect(() => {
    initializeFromCookies(cookies);
  }, [initializeFromCookies, cookies]);

  // 添加自定义事件监听器，处理文件版本更新
  useEffect(() => {
    const handleVersionUpdate = (event: any) => {
      const { fileId, versions, versionIndex } = event.detail;

      console.log('[ContentGeneratorClient] 收到文件版本更新事件:', {
        fileId,
        versionsCount: versions.length,
        versionIndex,
        versions: versions.map((v: any) => v.taskDescription)
      });

      // 更新generatedFiles状态
      setGeneratedFiles(prev => {
        const updatedFiles = prev.map(file => {
          if (file.id === fileId) {
            return {
              ...file,
              versions,
              currentVersionIndex: versionIndex,
              isModified: true
            };
          }
          return file;
        });

        console.log('[ContentGeneratorClient] 更新文件版本数组:', {
          fileId,
          versionsCount: versions.length,
          totalFiles: updatedFiles.length
        });

        return updatedFiles;
      });
    };

    // 添加事件监听器
    document.addEventListener('file-version-updated', handleVersionUpdate);

    // 清理函数
    return () => {
      document.removeEventListener('file-version-updated', handleVersionUpdate);
    };
  }, []);

  // 辅助函数：递归标记所有任务为completed
  const markAllCompleted = useCallback((ts: Task[]): Task[] => {
    return ts.map(t => ({
      ...t,
      status: 'completed',
      subtasks: t.subtasks ? markAllCompleted(t.subtasks) : undefined
    }));
  }, []);

  const [conversation, setConversation] = useState<Conversation>({
    id: 'new-conversation',
    messages: [],
    timestamp: Date.now(),
  });

  const [generatedFiles, setGeneratedFiles] = useState<GeneratedFile[]>([]);

  const [isGenerating, setIsGenerating] = useState(false);

  const [contentType, setContentType] = useState<'html' | 'markdown'>('html');

  const [tasks, setTasks] = useState<Task[]>([]);
  const [executionPhase, setExecutionPhase] = useState<TaskExecutionPhase>('planning');
  const [currentTaskIndex, setCurrentTaskIndex] = useState<number>(-1);

  const [fileOperations, setFileOperations] = useState<FileOperation[]>([]);

  // 创建文件版本 - 使用 useCallback 避免依赖问题
  const createFileVersion = useCallback((content: string, description: string, taskNumber?: number) => ({
    content,
    timestamp: Date.now(),
    taskDescription: description,
    taskNumber: taskNumber ?? currentTaskIndex + 1
  }), [currentTaskIndex]);

  // 初始化文件版本历史 - 使用 useCallback 避免依赖问题
  const initializeFileVersions = useCallback((content: string) => ({
    versions: [createFileVersion(content, '初始创建')],
    currentVersionIndex: 0
  }), [createFileVersion]);

  // 添加新版本
  const addNewVersion = (file: GeneratedFile, content: string, description: string) => {
    const versions = [...(file.versions || []), createFileVersion(content, description)];
    return {
      versions,
      currentVersionIndex: versions.length - 1
    };
  };

  const [styleOptions, setStyleOptions] = useState({
    style: '简约现代',
    complexity: '中等',
    fileCount: 1,
  });

  // 上下文窗口配置
  const [contextOptions, setContextOptions] = useState({
    maxMessages: 0, // 0表示不限制，使用全量历史
    keepSystemMessage: true, // 是否保留系统消息
  });

  // 面板宽度状态
  const [leftPanelWidth, setLeftPanelWidth] = useState(380);
  const [isDragging, setIsDragging] = useState(false);
  const dragStartXRef = useRef(0);
  const leftPanelWidthRef = useRef(380);

  // 处理拖拽事件
  useEffect(() => {
    if (!isDragging) return;

    const handleMouseMove = (e: MouseEvent) => {
      const delta = e.clientX - dragStartXRef.current;
      const newWidth = Math.max(320, Math.min(600, leftPanelWidthRef.current + delta));
      setLeftPanelWidth(newWidth);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.body.classList.remove('select-none');
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  const currentFileIndexRef = useRef<number>(0);

  const idCounterRef = useRef<number>(0);

  const getUserPrompt = () => {
    if (conversation.messages.length > 0) {
      const userMessages = conversation.messages.filter(msg => msg.role === 'user');
      if (userMessages.length > 0) {
        return userMessages[userMessages.length - 1].content;
      }
    }

    return contentType === 'html'
      ? '生成一个简单的Hello World网页'
      : '生成一个简单的Hello World文档';
  };

  const generatePromptWithFileInstructions = (userPrompt: string) => {
    const { fileInstructions, multiFileNote } = promptTemplates;
    const instructions = Object.values(fileInstructions).join('\n\n') + '\n' + multiFileNote;
    return `${userPrompt}\n\n${instructions}`;
  };

  const generateUniqueId = useCallback((prefix: string) => {
    const timestamp = Date.now();
    idCounterRef.current += 1;
    return `${prefix}-${timestamp}-${idCounterRef.current}`;
  }, []);

  const extractCodeFromConversation = useCallback(() => {
    console.log('Extracting code from conversation...');
    console.log('Content type:', contentType);
    console.log('Conversation messages:', conversation.messages.length);

    const assistantMessages = conversation.messages.filter(msg => msg.role === 'assistant');
    console.log('Assistant messages:', assistantMessages.length);

    if (assistantMessages.length === 0) {
      console.log('No assistant messages found');
      return null;
    }

    const lastMessage = assistantMessages[assistantMessages.length - 1].content;
    console.log('Last message length:', lastMessage.length);
    console.log('Last message preview:', lastMessage.substring(0, 100) + '...');

    console.log('Calling extractCodeFromMessage with content type:', contentType);
    const extractedCode = extractCodeFromMessage(lastMessage, contentType);
    console.log('Extracted code result:', extractedCode ? 'success' : 'null');
    if (extractedCode) {
      console.log('Extracted code length:', extractedCode.content.length);
      console.log('Extracted code preview:', extractedCode.content.substring(0, 100) + '...');
      if (extractedCode.filename) {
        console.log('Extracted filename from message:', extractedCode.filename);
      } else {
        console.log('No filename extracted from message');
      }
    }

    return extractedCode;
  }, [conversation.messages, contentType]);

  const extractTasksFromResponse = (response: string): Task[] => {
    console.log('从模型响应中提取任务...');

    const taskMap: Record<string, Task> = {};

    const topLevelTasks: Task[] = [];

    let lastTaskAtLevel: Record<number, Task> = {};

    const taskProtocolRegex = /【任务(\d+(?:\.\d+)*)】([^\n]+)/g;

    let match;

    while ((match = taskProtocolRegex.exec(response)) !== null) {
      const taskNumberStr = match[1];
      let taskDescription = match[2].trim();

      const taskParts = taskNumberStr.split('.');
      const taskNumber = parseInt(taskParts[0]);
      const level = taskParts.length - 1;

      const taskIdentifier = taskNumberStr;

      taskDescription = cleanTaskDescription(taskDescription);

      const task: Task = {
        id: generateUniqueId('task'),
        number: taskNumber,
        description: taskDescription,
        status: 'pending',
        level
      };

      taskMap[taskIdentifier] = task;

      if (level === 0) {
        topLevelTasks.push(task);
      } else {
        const parentParts = taskParts.slice(0, -1);
        const parentIdentifier = parentParts.join('.');

        if (taskMap[parentIdentifier]) {
          task.parentId = taskMap[parentIdentifier].id;
          if (!taskMap[parentIdentifier].subtasks) {
            taskMap[parentIdentifier] = {
                ...taskMap[parentIdentifier],
                subtasks: []
              };
          }
          taskMap[parentIdentifier].subtasks!.push(task);
        } else {
          console.warn(`找不到任务 ${taskNumberStr} 的父任务 ${parentIdentifier}，将其作为顶级任务处理`);
          topLevelTasks.push(task);
        }
      }

      lastTaskAtLevel[level] = task;
    }

    if (topLevelTasks.length === 0) {
      console.log('未找到符合协议的任务标记，尝试使用备用提取方法...');

      // 添加规划阶段任务格式的正则表达式
      const planningPhaseRegex = /【任务(\d+(?:\.\d+)*)】\s*([^\n]+)/g;

      // 尝试匹配规划阶段的任务格式
      while ((match = planningPhaseRegex.exec(response)) !== null) {
        const taskNumberStr = match[1];
        let taskDescription = match[2].trim();

        const taskParts = taskNumberStr.split('.');
        const taskNumber = parseInt(taskParts[0]);
        const level = taskParts.length - 1;

        const taskIdentifier = taskNumberStr;

        taskDescription = cleanTaskDescription(taskDescription);

        const task: Task = {
          id: generateUniqueId('task'),
          number: taskNumber,
          description: taskDescription,
          status: 'pending',
          level
        };

        taskMap[taskIdentifier] = task;

        if (level === 0) {
          topLevelTasks.push(task);
        } else {
          const parentParts = taskParts.slice(0, -1);
          const parentIdentifier = parentParts.join('.');

          if (taskMap[parentIdentifier]) {
            task.parentId = taskMap[parentIdentifier].id;
            if (!taskMap[parentIdentifier].subtasks) {
              taskMap[parentIdentifier] = {
                ...taskMap[parentIdentifier],
                subtasks: []
              };
            }
            taskMap[parentIdentifier].subtasks!.push(task);
          } else {
            console.warn(`找不到任务 ${taskNumberStr} 的父任务 ${parentIdentifier}，将其作为顶级任务处理`);
            topLevelTasks.push(task);
          }
        }

        lastTaskAtLevel[level] = task;
      }

      // 如果仍然没有找到任务，尝试使用其他备用方法
      if (topLevelTasks.length === 0) {
        console.log('未找到规划阶段的任务标记，继续尝试其他备用提取方法...');

        const fallbackRegex1 = /^(\s*)(?:(?:Task|任务|步骤|TASK|STEP)\s*)?(\d+(?:\.\d+)*)(?:[\.、)）\:\：]\s*|\. +|\s+[-–]\s*|\s+)([^\n]+)$/gm;

        const taskNumbers = new Set<number>();

        // 尝试匹配常规的任务格式
        while ((match = fallbackRegex1.exec(response)) !== null) {
          const indentation = match[1] || '';
          const level = Math.floor(indentation.length / 2);
          const taskNumberStr = match[2];
          let taskDescription = match[3].trim();

          const taskParts = taskNumberStr.split('.');
          const taskNumber = parseInt(taskParts[0]);

          if (taskNumbers.has(taskNumber)) {
            console.log(`发现重复任务编号: ${taskNumber}，跳过`);
            continue;
          }

          taskNumbers.add(taskNumber);

          taskDescription = cleanTaskDescription(taskDescription);

          const task: Task = {
            id: generateUniqueId('task'),
            number: taskNumber,
            description: taskDescription,
            status: 'pending',
            level
          };

          taskMap[taskNumber.toString()] = task;

          if (level === 0 || !lastTaskAtLevel[level - 1]) {
            topLevelTasks.push(task);
          } else {
            const parentTask = lastTaskAtLevel[level - 1];
            task.parentId = parentTask.id;
            if (!parentTask.subtasks) {
              parentTask.subtasks = [];
            }
            parentTask.subtasks.push(task);
          }

          lastTaskAtLevel[level] = task;
        }
      }

      if (topLevelTasks.length === 0) {
        console.log('备用方法1也失败，尝试最简单的任务提取...');

        const fallbackRegex2 = /(?:^|\n)(?:Task\s*)?(\d+)(?:[\.\u3001)\:\\uff1a]\s*)([^\n]+)/g;

        // 创建新的任务编号集合，避免使用上一个作用域的变量
        const taskNumberSet = new Set<number>();

        while ((match = fallbackRegex2.exec(response)) !== null) {
          const taskNumber = parseInt(match[1]);
          let taskDescription = match[2].trim();

          if (taskNumberSet.has(taskNumber)) {
            console.log(`发现重复任务编号: ${taskNumber}，跳过`);
            continue;
          }

          taskNumberSet.add(taskNumber);

          taskDescription = cleanTaskDescription(taskDescription);

          const task: Task = {
            id: generateUniqueId('task'),
            number: taskNumber,
            description: taskDescription,
            status: 'pending',
            level: 0
          };

          taskMap[taskNumber.toString()] = task;
          topLevelTasks.push(task);
        }
      }
    }

    topLevelTasks.sort((a, b) => a.number - b.number);

    const sortSubtasks = (tasks: Task[]) => {
      if (!tasks || tasks.length === 0) return;

      tasks.sort((a, b) => a.number - b.number);

      for (const task of tasks) {
        if (task.subtasks && task.subtasks.length > 0) {
          sortSubtasks(task.subtasks);
        }
      }
    };

    for (const task of topLevelTasks) {
      if (task.subtasks && task.subtasks.length > 0) {
        sortSubtasks(task.subtasks);
      }
    }

    const validatedTasks = validateTasks(topLevelTasks);

    console.log(`提取到 ${validatedTasks.length} 个顶级任务，总计 ${Object.keys(taskMap).length} 个任务（含子任务）`);

    return validatedTasks;
  };

  const validateTasks = (tasks: Task[]): Task[] => {
    const isValidTask = (task: Task): boolean => {
      if (isNaN(task.number) || task.number <= 0) {
        console.warn(`任务验证失败：无效的任务编号 ${task.number}`);
        return false;
      }

      if (!task.description || task.description.trim() === '') {
        console.warn(`任务验证失败：任务 ${task.number} 描述为空`);
        return false;
      }

      if (task.description.length < 5) {
        console.warn(`任务验证失败：任务 ${task.number} 描述过短 "${task.description}"`);
        return false;
      }

      const nonTaskPatterns = [
        /^(?:第|chapter|section)\s*\d+\s*(?:章|节|部分|section|part)/i,
        /^(?:目录|contents|table of contents)/i,
        /^(?:注意|note|attention|warning)/i,
        /^(?:总结|summary|conclusion)/i
      ];

      for (const pattern of nonTaskPatterns) {
        if (pattern.test(task.description)) {
          console.warn(`任务验证失败：任务 ${task.number} 描述匹配非任务模式 "${task.description}"`);
          return false;
        }
      }

      return true;
    };

    const validateTasksRecursive = (tasks: Task[]): Task[] => {
      return tasks.filter(task => {
        const isValid = isValidTask(task);

        if (isValid && task.subtasks && task.subtasks.length > 0) {
          task.subtasks = validateTasksRecursive(task.subtasks);
        }

        return isValid;
      });
    };

    return validateTasksRecursive(tasks);
  };

  const cleanTaskDescription = (description: string): string => {
    let cleanedDescription = description;

    cleanedDescription = cleanedDescription.replace(/<[^>]*>/g, '');

    cleanedDescription = cleanedDescription.replace(/[<>]/g, '');

    cleanedDescription = cleanedDescription.replace(/["']/g, '');

    cleanedDescription = cleanedDescription.replace(/(\d+)["'><]/g, '');

    cleanedDescription = cleanedDescription.replace(/\s+/g, ' ').trim();

    if (!cleanedDescription.trim()) {
      return '未指定任务描述';
    }

    return cleanedDescription;
  };

  const areAllTasksCompleted = useCallback((tasks: Task[]): boolean => {
    return tasks.every(task =>
      task.status === 'completed' && (!task.subtasks || areAllTasksCompleted(task.subtasks))
    );
  }, []);

  const addFileOperation = useCallback((type: FileOperationType, fileName: string, fileType: string, taskNumber?: number) => {
    const operation: FileOperation = {
      id: generateUniqueId('op'),
      type,
      fileName,
      fileType,
      status: 'pending',
      taskNumber,
      timestamp: Date.now()
    };

    setFileOperations(prev => [...prev, operation]);
    return operation.id;
  }, [generateUniqueId]);

  const updateFileOperationStatus = useCallback((operationId: string, status: FileOperationStatus) => {
    setFileOperations(prev =>
      prev.map(op => op.id === operationId ? { ...op, status } : op)
    );
  }, []);

  const renderTaskList = useCallback((tasks: any[], level = 0): string => {
    return tasks.map(task => {
      const checked = task.status === 'completed' ? '[x]' : '[ ]';
      const indent = '  '.repeat(level);

      let cleanDescription = task.description || '';

      cleanDescription = cleanDescription.replace(/<[^>]*>/g, '');

      cleanDescription = cleanDescription.replace(/[<>]/g, '');

      cleanDescription = cleanDescription.replace(/["']/g, '');

      cleanDescription = cleanDescription.replace(/(\d+)["'><]/g, '');

      if (!cleanDescription.trim()) {
        cleanDescription = '任务' + (task.number || '');
      }

      let line = `${indent}- ${checked} ${cleanDescription}`;
      if (task.subtasks && Array.isArray(task.subtasks) && task.subtasks.length > 0) {
        line += '\n' + renderTaskList(task.subtasks, level + 1);
      }
      return line;
    }).join('\n');
  }, []);

  const updateTodoFile = useCallback((createOperation = false) => {
    // 如果任务列表为空，不创建或更新Todo.md文件
    if (tasks.length === 0) {
      return;
    }

    const allCompleted = areAllTasksCompleted(tasks);
    let tasksForMd = [...tasks]; // 创建任务的副本

    if (allCompleted) {
      // 只为显示创建已完成的任务副本，不更新原始状态
      tasksForMd = markAllCompleted(tasks);
    }

    const deduplicateTasks = (ts: Task[]): Task[] => {
      const taskMap = new Map<number, Task>();

      const processTasks = (taskList: Task[]) => {
        taskList.forEach(task => {
          if (!taskMap.has(task.number)) {
            taskMap.set(task.number, task);

            if (task.subtasks && task.subtasks.length > 0) {
              task.subtasks = deduplicateTasks(task.subtasks);
            }
          } else {
            const existingTask = taskMap.get(task.number)!;
            if (task.status === 'completed' && existingTask.status !== 'completed') {
              existingTask.status = 'completed';
            }

            if (task.subtasks && task.subtasks.length > 0) {
              if (!existingTask.subtasks) {
                existingTask.subtasks = [];
              }
              existingTask.subtasks = deduplicateTasks([...existingTask.subtasks, ...task.subtasks]);
            }
          }
        });
      };

      processTasks(ts);

      return Array.from(taskMap.values()).sort((a, b) => a.number - b.number);
    };

    const dedupedTasks = deduplicateTasks(tasksForMd);

    const todoContent = `## 任务清单\n\n${renderTaskList(dedupedTasks)}`;

    const todoFileIndex = generatedFiles.findIndex(file => file.name === 'Todo.md');

    let operationId: string | undefined;

    if (createOperation) {
      setFileOperations(prev => prev.filter(op => op.fileName !== 'Todo.md'));
      const operationType = todoFileIndex >= 0 ? 'update' : 'create';
      operationId = addFileOperation(
        operationType as FileOperationType,
        'Todo.md',
        'markdown',
        undefined
      );
      updateFileOperationStatus(operationId, 'in-progress');
    }

    if (todoFileIndex >= 0) {
      const existingFile = generatedFiles[todoFileIndex];

      const lastVersion = existingFile.versions && existingFile.versions.length > 0
        ? existingFile.versions[existingFile.versions.length - 1]
        : { content: existingFile.content };

      if (lastVersion.content !== todoContent) {
        const newVersion = createFileVersion(todoContent, '更新任务状态');
        const versions = existingFile.versions || [{
          content: existingFile.content,
          timestamp: existingFile.timestamp,
          taskDescription: '初始版本'
        }];

        const updatedTodoFile: GeneratedFile = {
          ...existingFile,
          content: todoContent,
          timestamp: Date.now(),
          versions: [...versions, newVersion],
          currentVersionIndex: versions.length,
          isModified: true
        };

        setGeneratedFiles(prev => {
          const newFiles = [...prev];
          newFiles[todoFileIndex] = updatedTodoFile;
          return newFiles;
        });
      }
    } else {
      const todoFile: GeneratedFile = {
        id: generateUniqueId('file'),
        name: 'Todo.md',
        description: 'Markdown - 任务清单',
        content: todoContent,
        contentType: 'markdown',
        status: 'completed' as FileStatus,
        order: generatedFiles.length,
        viewMode: 'preview',
        timestamp: Date.now(),
        ...initializeFileVersions(todoContent),
        isModified: false
      };

      setGeneratedFiles(prev => [...prev, todoFile]);
    }

    if (allCompleted) {
      setFileOperations(prev => prev.map(op =>
        op.fileName === 'Todo.md' && op.status !== 'completed'
          ? { ...op, status: 'completed' as FileOperationStatus }
          : op
      ));
    }
  }, [tasks, generatedFiles, setFileOperations, addFileOperation, updateFileOperationStatus, generateUniqueId, renderTaskList, areAllTasksCompleted, markAllCompleted, createFileVersion, initializeFileVersions]);

  // 提取文件函数 - 移除 useCallback 以避免依赖问题
  const extractMultipleFilesFromConversation = () => {
    console.log('=== 开始从对话中提取文件 ===');

    const assistantMessages = conversation.messages.filter(msg => msg.role === 'assistant');
    if (assistantMessages.length === 0) {
      console.log('没有找到助手消息');
      return [];
    }

    const lastMessage = assistantMessages[assistantMessages.length - 1];
    console.log('处理最后一条消息:', {
      id: lastMessage.id,
      length: lastMessage.content.length,
      content: lastMessage.content
    });

    const codeBlockMatches = lastMessage.content.match(/```(html|markdown|md)\s*\{\s*filename\s*=\s*[^}]+\s*\}/g) || [];
    console.log('找到的代码块标记:', codeBlockMatches);

    const extractedFiles = extractMultipleFilesFromMessage(lastMessage.content, lastMessage.id);

    if (extractedFiles.length > 0) {
      console.log('成功提取到文件:', extractedFiles.map(f => ({
        filename: f.filename,
        contentType: f.contentType,
        contentLength: f.content.length,
        contentPreview: f.content.substring(0, 100)
      })));
      return extractedFiles;
    }

    console.log('没有找到多个文件，尝试提取单个文件');
    const singleFile = extractCodeFromMessage(lastMessage.content, contentType);
    if (singleFile) {
      console.log('找到单个文件:', {
        filename: singleFile.filename,
        contentLength: singleFile.content.length
      });
      return [{
        ...singleFile,
        contentType,
        messageId: lastMessage.id,
        isNew: true
      }];
    }

    console.log('没有找到任何文件');
    return [];
  };

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage = {
      id: `msg-${Date.now()}`,
      role: 'user' as const,
      content: content,
      timestamp: Date.now(),
      type: 'user' as 'user' | 'assistant' | 'task' | 'system'
    };
    const updatedConversation = {
      ...conversation,
      messages: [...conversation.messages, userMessage],
    };

    setConversation(updatedConversation);
    setIsGenerating(true);

    setTasks([]);
    setExecutionPhase('planning');
    setCurrentTaskIndex(-1);

    setFileOperations([]);

    try {
      // 根据上下文窗口配置处理消息
      let messagesToSend = [...updatedConversation.messages];

      // 如果设置了最大消息数量限制且大于0
      if (contextOptions.maxMessages > 0 && messagesToSend.length > contextOptions.maxMessages) {
        // 保存系统消息(如果有且需要保留)
        const systemMessages = contextOptions.keepSystemMessage
          ? messagesToSend.filter(msg => msg.role === 'system')
          : [];

        // 获取最近的N条非系统消息
        const recentMessages = messagesToSend
          .filter(msg => msg.role !== 'system')
          .slice(-contextOptions.maxMessages);

        // 合并系统消息和最近消息
        messagesToSend = [...systemMessages, ...recentMessages];

        console.log(`应用上下文窗口: 从 ${updatedConversation.messages.length} 条消息中选择 ${messagesToSend.length} 条`);
      }

      const apiMessages = [
        ...messagesToSend.map(msg => ({ role: msg.role, content: msg.content })),
        { role: 'user' as const, content: generatePromptWithFileInstructions(content) },
      ];
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: apiMessages, model }), // 使用从顶层获取的model
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const data = await response.json();
      const aiResponseContent = data.message.content;

      const hasHtmlCode = aiResponseContent.includes('```html') ||
                       aiResponseContent.includes('<!DOCTYPE html') ||
                       aiResponseContent.includes('<html');
      const hasMarkdownCode = aiResponseContent.includes('```markdown') ||
                           aiResponseContent.includes('```md') ||
                           aiResponseContent.includes('# ');

      if (hasHtmlCode && contentType !== 'html') {
        setContentType('html');
      } else if (hasMarkdownCode && contentType !== 'markdown') {
        setContentType('markdown');
      }

      const messageId = `msg-${Date.now()}`;

      setConversation(prev => ({
        ...prev,
        messages: [
          ...prev.messages,
          {
            id: messageId,
            role: 'assistant' as const,
            content: aiResponseContent,
            timestamp: Date.now(),
            type: 'assistant' as 'user' | 'assistant' | 'task' | 'system'
          }
        ]
      }));

      const extractedTasks = extractTasksFromResponse(aiResponseContent);

      if (extractedTasks.length > 0) {
        setTasks(extractedTasks);

        updateTodoFile(true);

        setExecutionPhase('executing');
        setCurrentTaskIndex(0);
      } else {
        const extractedFiles = extractMultipleFilesFromMessage(aiResponseContent, messageId);

        if (extractedFiles.length > 0) {
          console.log('本轮提取到文件数:', extractedFiles.length);

          const htmlFiles = extractedFiles.filter(file => file.contentType === 'html');
          const mdFiles = extractedFiles.filter(file => file.contentType === 'markdown');

          console.log(`提取到 ${htmlFiles.length} 个HTML文件和 ${mdFiles.length} 个Markdown文件`);

          if (htmlFiles.length > 0) {
            const fileName = htmlFiles.length === 1 && htmlFiles[0].filename
              ? htmlFiles[0].filename
              : 'index.html';

            setFileOperations(prev => prev.filter(op => op.fileName !== fileName));

            const operationType = 'create';
            const operationId = addFileOperation(
              operationType as FileOperationType,
              fileName,
              'html',
              undefined
            );

            updateFileOperationStatus(operationId, 'in-progress');

            const existingFileIndex = generatedFiles.findIndex(
              f => f.name === fileName && f.contentType === 'html'
            );

            if (existingFileIndex >= 0) {
              const existingFile = generatedFiles[existingFileIndex];

              const lastVersion = existingFile.versions && existingFile.versions.length > 0
                ? existingFile.versions[existingFile.versions.length - 1]
                : { content: existingFile.content };

              if (lastVersion.content !== htmlFiles[htmlFiles.length - 1].content) {
                const newVersion = createFileVersion(
                  htmlFiles[htmlFiles.length - 1].content,
                  '更新HTML内容',
                  currentTaskIndex + 1
                );

                const versions = existingFile.versions || [{
                  content: existingFile.content,
                  timestamp: existingFile.timestamp,
                  taskDescription: '初始版本'
                }];

                const updatedFile: GeneratedFile = {
                  ...existingFile,
                  content: htmlFiles[htmlFiles.length - 1].content,
                  timestamp: Date.now(),
                  versions: [...versions, newVersion],
                  currentVersionIndex: versions.length,
                  isModified: true
                };

                setGeneratedFiles(prev => {
                  const newFiles = [...prev];
                  newFiles[existingFileIndex] = updatedFile;
                  return newFiles;
                });
              }
            } else {
              const newFile: GeneratedFile = {
                id: generateUniqueId('file'),
                name: fileName,
                description: 'HTML - index.html',
                content: htmlFiles[htmlFiles.length - 1].content,
                contentType: 'html',
                status: 'completed' as FileStatus,
                order: generatedFiles.length,
                viewMode: 'preview',
                timestamp: Date.now(),
                ...initializeFileVersions(htmlFiles[htmlFiles.length - 1].content),
                isModified: false
              };

              setGeneratedFiles(prev => [...prev, newFile]);
            }

            updateFileOperationStatus(operationId, 'completed');
          }

          for (const [index, file] of mdFiles.entries()) {
            const fileName = file.filename || `index${index > 0 ? index : ''}.md`;

            if (fileName === 'Todo.md' || fileName === 'summary.md') {
              console.log(`跳过特殊文件: ${fileName}`);
              continue;
            }

            const existingFileIndex = generatedFiles.findIndex(
              f => f.name === fileName && f.contentType === file.contentType
            );

            console.log(`${fileName} 文件${existingFileIndex >= 0 ? '已存在' : '不存在'}`);

            const operationType = existingFileIndex >= 0 ? 'update' : 'create';
            const operationId = addFileOperation(
              operationType as FileOperationType,
              fileName,
              file.contentType,
              undefined
            );

            updateFileOperationStatus(operationId, 'in-progress');

            if (existingFileIndex >= 0) {
              const existingFile = generatedFiles[existingFileIndex];

              const lastVersion = existingFile.versions && existingFile.versions.length > 0
                ? existingFile.versions[existingFile.versions.length - 1]
                : { content: existingFile.content };

              if (lastVersion.content !== file.content) {
                const newVersion = createFileVersion(
                  file.content,
                  '更新Markdown内容',
                  currentTaskIndex + 1
                );

                const versions = existingFile.versions || [{
                  content: existingFile.content,
                  timestamp: existingFile.timestamp,
                  taskDescription: '初始版本'
                }];

                const updatedFile: GeneratedFile = {
                  ...existingFile,
                  content: file.content,
                  timestamp: Date.now(),
                  versions: [...versions, newVersion],
                  currentVersionIndex: versions.length,
                  isModified: true
                };

                setGeneratedFiles(prev => {
                  const newFiles = [...prev];
                  newFiles[existingFileIndex] = updatedFile;
                  return newFiles;
                });
              }
            } else {
              const newFile: GeneratedFile = {
                id: generateUniqueId('file'),
                name: fileName,
                description: `Markdown - ${fileName}`,
                content: file.content,
                contentType: file.contentType,
                status: 'completed' as FileStatus,
                order: generatedFiles.length,
                viewMode: 'preview',
                timestamp: Date.now(),
                ...initializeFileVersions(file.content),
                isModified: false
              };

              setGeneratedFiles(prev => [...prev, newFile]);
            }

            updateFileOperationStatus(operationId, 'completed');
          }
        } else {
          console.log('本轮未提取到任何文件');
        }
      }
    } catch (error) {
      console.error('Error getting AI response:', error);

      setConversation(prev => ({
        ...prev,
        messages: [
          ...prev.messages,
          {
            id: `msg-${Date.now()}`,
            role: 'assistant' as const,
            content: `抱歉，我暂时无法处理您的请求。当前使用的模型是 ${model}。请尝试重新发送消息或更换模型。`, // 使用从顶层获取的model
            timestamp: Date.now(),
            type: 'assistant' as 'user' | 'assistant' | 'task' | 'system'
          }
        ]
      }));
    } finally {
      setIsGenerating(false);
    }
  };

  const handleGenerateContent = async () => {
    try {
      setIsGenerating(true);
      console.log('=== 开始生成内容 ===');

      if (conversation.messages.length === 0) {
        console.log('没有任何消息');
        return;
      }

      const lastMessage = conversation.messages[conversation.messages.length - 1];
      if (lastMessage.role !== 'assistant') {
        console.log('最后一条消息不是助手消息');
        return;
      }

      console.log('处理消息:', {
        role: lastMessage.role,
        contentLength: lastMessage.content.length
      });

      const extractedFiles = extractMultipleFilesFromConversation();
      console.log('=== 文件提取信息 ===');
      console.log('对话消息总数:', conversation.messages.length);
      console.log('助手消息数:', conversation.messages.filter(msg => msg.role === 'assistant').length);
      console.log('提取到的文件数量:', extractedFiles.length);

      if (extractedFiles.length > 0) {
        console.log('找到文件:', extractedFiles.length, '个');

        const updatedFiles: GeneratedFile[] = [];

        for (const [index, file] of extractedFiles.entries()) {
          const fileName = file.filename || (file.contentType === 'html' ? 'index.html' : `index${index > 0 ? index : ''}.md`);

          console.log(`处理文件 ${index + 1}:`, {
            fileName,
            contentType: file.contentType,
            contentLength: file.content.length
          });

          const existingFileIndex = generatedFiles.findIndex(
            f => f.name === fileName && f.contentType === file.contentType
          );

          if (existingFileIndex >= 0) {
            const existingFile = generatedFiles[existingFileIndex];

            const lastVersion = existingFile.versions && existingFile.versions.length > 0
              ? existingFile.versions[existingFile.versions.length - 1]
              : { content: existingFile.content };

            if (lastVersion.content !== file.content) {
              const newVersion = createFileVersion(file.content, '更新生成的内容');
              const versions = existingFile.versions || [{
                content: existingFile.content,
                timestamp: existingFile.timestamp,
                taskDescription: '初始版本'
              }];

              const updatedFile: GeneratedFile = {
                ...existingFile,
                content: file.content,
                timestamp: Date.now(),
                versions: [...versions, newVersion],
                currentVersionIndex: versions.length,
                isModified: true
              };

              updatedFiles.push(updatedFile);
            }
          } else {
            const newFile: GeneratedFile = {
              id: generateUniqueId('file'),
              name: fileName,
              description: `${file.contentType === 'html' ? 'HTML' : 'Markdown'} - ${fileName}`,
              content: file.content,
              contentType: file.contentType,
              status: 'completed' as FileStatus,
              order: generatedFiles.length + updatedFiles.length,
              viewMode: 'preview',
              timestamp: Date.now(),
              ...initializeFileVersions(file.content),
              isModified: false
            };

            updatedFiles.push(newFile);
          }
        }

        setGeneratedFiles(prev => {
          const filteredFiles = prev.filter(file =>
            !updatedFiles.some(updatedFile =>
              updatedFile.name === file.name && updatedFile.contentType === file.contentType
            )
          );

          return [...filteredFiles, ...updatedFiles];
        });

        console.log('文件列表已更新');
        return;
      }

      const userPrompt = getUserPrompt();
      const defaultContent = contentType === 'html'
        ? `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hello World</title>
  <style>
    body { font-family: system-ui, sans-serif; line-height: 1.5; padding: 2rem; max-width: 800px; margin: 0 auto; }
    h1 { color: #2563eb; }
    p { margin-bottom: 1rem; }
    .container { border: 1px solid #e5e7eb; border-radius: 0.5rem; padding: 1.5rem; }
  </style>
</head>
<body>
  <h1>Hello World</h1>
  <div class="container">
    <p>请在对话中输入HTML代码来生成内容。</p>
    <p>用户提示: ${userPrompt}</p>
  </div>
</body>
</html>`
        : `# Hello World

请在对话中输入Markdown代码来生成内容。

## 用户提示

${userPrompt}

## 内容类型

- 类型: Markdown
- 文件: hello.md
- 描述: Hello World
`;

      const defaultFileName = contentType === 'html' ? 'index.html' : 'index.md';

      const existingFileIndex = generatedFiles.findIndex(
        f => f.name === defaultFileName && f.contentType === contentType
      );

      const operationType = existingFileIndex >= 0 ? 'update' : 'create';
      const operationId = addFileOperation(
        operationType as FileOperationType,
        defaultFileName,
        contentType,
        undefined
      );

      updateFileOperationStatus(operationId, 'in-progress');

      if (existingFileIndex >= 0) {
        const existingFile = generatedFiles[existingFileIndex];

        const lastVersion = existingFile.versions && existingFile.versions.length > 0
          ? existingFile.versions[existingFile.versions.length - 1]
          : { content: existingFile.content };

        if (lastVersion.content !== defaultContent) {
          const newVersion = createFileVersion(defaultContent, '更新默认内容');
          const versions = existingFile.versions || [{
            content: existingFile.content,
            timestamp: existingFile.timestamp,
            taskDescription: '初始版本'
          }];

          const updatedFile: GeneratedFile = {
            ...existingFile,
            content: defaultContent,
            timestamp: Date.now(),
            versions: [...versions, newVersion],
            currentVersionIndex: versions.length,
            isModified: true
          };

          setGeneratedFiles(prev => prev.map((file, index) =>
            index === existingFileIndex ? updatedFile : file
          ));
        }
      } else {
        const newFile: GeneratedFile = {
          id: generateUniqueId('file'),
          name: defaultFileName,
          description: contentType === 'html' ? 'HTML文件' : 'Markdown文件',
          content: defaultContent,
          contentType,
          status: 'completed' as FileStatus,
          order: 0,
          viewMode: 'preview' as 'code' | 'preview' | 'split',
          timestamp: Date.now(),
          ...initializeFileVersions(defaultContent),
          isModified: false
        };

        setGeneratedFiles(prev => [...prev, newFile]);
      }

      updateFileOperationStatus(operationId, 'completed');

      console.log('创建了默认文件');
    } catch (error) {
      console.error('生成内容时出错:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleViewModeChange = (fileId: string, viewMode: 'code' | 'preview' | 'split') => {
    setGeneratedFiles(prev =>
      prev.map(file =>
        file.id === fileId ? { ...file, viewMode } : file
      )
    );
  };

  /**
   * 处理文件版本切换
   * 1. 找到目标文件
   * 2. 合并同名文件的所有版本历史
   * 3. 按时间排序并切换到指定版本
   * 4. 更新文件内容和版本信息
   */
  const handleVersionChange = useCallback((fileId: string, versionIndex: number) => {
    console.log('[handleVersionChange] 请求切换版本:', { fileId, versionIndex });

    // 1. 找到目标文件，并获取其基本信息
    const targetFile = generatedFiles.find(f => f.id === fileId);
    if (!targetFile) {
      console.error('[handleVersionChange] 未找到目标文件:', fileId);
      return;
    }

    // 2. 合并同名文件的版本历史
    const sameNameFiles = generatedFiles.filter(f =>
      f.name === targetFile.name && f.contentType === targetFile.contentType
    );

    // 3. 合并所有版本并按时间排序
    // 定义版本类型
    type FileVersion = NonNullable<GeneratedFile['versions']>[number];

    // 合并所有版本
    const allVersions = sameNameFiles.reduce<FileVersion[]>((acc, file) => {
      if (file.versions) {
        acc.push(...file.versions);
      } else if (file.content) {
        // 如果文件没有版本历史，将当前内容作为一个版本
        acc.push({
          content: file.content,
          timestamp: file.timestamp,
          taskDescription: '初始版本'
        });
      }
      return acc;
    }, []).sort((a, b) => a.timestamp - b.timestamp);

    // 4. 验证版本索引的有效性
    if (!allVersions.length) {
      console.error('[handleVersionChange] 文件没有任何可用版本:', {
        fileId,
        name: targetFile.name,
        contentType: targetFile.contentType
      });
      return;
    }

    if (versionIndex < 0 || versionIndex >= allVersions.length) {
      console.error('[handleVersionChange] 无效的版本索引:', {
        versionIndex,
        totalVersions: allVersions.length
      });
      return;
    }

    // 5. 获取目标版本
    const targetVersion = allVersions[versionIndex];

    console.log('[handleVersionChange] 切换版本详情:', {
      fileId,
      fileName: targetFile.name,
      fromVersion: targetFile.currentVersionIndex,
      toVersion: versionIndex,
      totalVersions: allVersions.length,
      versionTimestamp: targetVersion.timestamp,
      versionDescription: targetVersion.taskDescription
    });

    // 6. 更新文件内容和版本信息
    setGeneratedFiles(prev => {
      const updatedFiles = prev.map(file => {
        if (file.id === fileId) {
          return {
            ...file,
            content: targetVersion.content,
            versions: allVersions,
            currentVersionIndex: versionIndex,
            isModified: true
          };
        }
        return file;
      });

      console.log('[handleVersionChange] 更新完成:', {
        updatedFileId: fileId,
        newVersionIndex: versionIndex,
        totalFiles: updatedFiles.length
      });

      return updatedFiles;
    });
  }, [generatedFiles]);

  useEffect(() => {
    const executeCurrentTask = async () => {
      if (executionPhase === 'executing' && currentTaskIndex >= 0 && currentTaskIndex < tasks.length) {
        const currentTask = tasks[currentTaskIndex];

        const success = await executeTask(
          currentTask,
          setTasks,
          updateTodoFile,
          generateUniqueId,
          setConversation,
          conversation,
          setIsGenerating,
          { ...styleOptions, model }, // 使用从顶层获取的model
          extractMultipleFilesFromMessage,
          generateDefaultFileName,
          setGeneratedFiles,
          generatedFiles
        );

        if (success) {
          if (currentTaskIndex < tasks.length - 1) {
            setCurrentTaskIndex(currentTaskIndex + 1);
          } else {
            await generateSummary(
              tasks,
              setExecutionPhase,
              generateUniqueId,
              setConversation,
              conversation,
              setIsGenerating,
              { ...styleOptions, model }, // 使用从顶层获取的model
              setGeneratedFiles,
              generatedFiles
            );
          }
        }
      }
    };

    if (!isGenerating) {
      executeCurrentTask();
    }
  }, [executionPhase, currentTaskIndex, tasks, isGenerating, conversation, generatedFiles, styleOptions, model, updateTodoFile, generateUniqueId]); // 添加model作为依赖

  useEffect(() => {
    // 只有当任务列表不为空时才更新Todo.md
    if (tasks.length > 0) {
      updateTodoFile(false); // 不创建新操作，仅同步内容
    } else {
      // 如果任务列表为空且Todo.md存在，则从生成的文件列表中移除它
      const todoFileIndex = generatedFiles.findIndex(file => file.name === 'Todo.md');
      if (todoFileIndex >= 0) {
        setGeneratedFiles(prev => prev.filter(file => file.name !== 'Todo.md'));
      }
    }
  }, [tasks, updateTodoFile, generatedFiles]);

  return (
    <div className="flex flex-col h-screen bg-gray-900 text-gray-100">
      <header className="bg-gradient-to-r from-indigo-900 via-purple-900 to-indigo-800 border-b border-indigo-700 p-4 flex justify-between items-center shadow-lg">
        <h1 className="text-xl font-bold text-white flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2h-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12a3 3 0 1 0 6 0 3 3 0 0 0-6 0z" />
          </svg>
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-400 font-extrabold">面向过程文件的AIGC原生内容生成智能体</span>
        </h1>
        <div className="flex items-center gap-2">
          <AISettings />
        </div>
      </header>

      <main className="flex flex-1 overflow-hidden">
        <div className="flex flex-1 overflow-hidden">
          {/* 左侧对话面板 - 宽度由状态控制 */}
          <div
            className="flex-shrink-0 overflow-hidden bg-gray-800 border-r border-gray-700"
            style={{ width: `${leftPanelWidth}px` }}
          >
            <ConversationPanel
              conversation={conversation}
              onSendMessage={handleSendMessage}
              contentType={contentType}
              setContentType={setContentType}
              styleOptions={styleOptions}
              setStyleOptions={setStyleOptions}
              contextOptions={contextOptions}
              setContextOptions={setContextOptions}
              onGenerateContent={handleGenerateContent}
              isGenerating={isGenerating}
              tasks={tasks}
              executionPhase={executionPhase}
              fileOperations={fileOperations}
            />
          </div>

          {/* 可拖拽分隔线 */}
          <div
            className={`w-4 flex items-center justify-center cursor-col-resize transition-colors ${isDragging ? 'bg-indigo-600' : 'bg-gray-800 hover:bg-indigo-800'}`}
            onMouseDown={(e) => {
              e.preventDefault(); // 防止默认行为
              setIsDragging(true);
              dragStartXRef.current = e.clientX;
              leftPanelWidthRef.current = leftPanelWidth;

              // 防止文本选择
              document.body.classList.add('select-none');
            }}
          >
            {/* 拖拽指示线 */}
            <div className="h-10 w-[2px] bg-cyan-400 rounded-full glow-effect"></div>
          </div>

          {/* 右侧内容预览面板 */}
          <div className="flex-1 overflow-auto bg-gray-850">
            <ContentViewerPanel
              files={generatedFiles}
              isGenerating={isGenerating}
              onViewModeChange={handleViewModeChange}
              onVersionChange={handleVersionChange}
            />
          </div>
        </div>
      </main>
    </div>
  );
}
