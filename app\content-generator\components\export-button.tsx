"use client";

import React, { useState } from 'react';
import { GeneratedFile } from '../types';
import ExportModal from './ExportModal';
import { isSlideContent, generatePDFHTML, generatePPTHTML as importedGeneratePPTHTML } from '../../../lib/export-utils';

interface ExportButtonProps {
  files: GeneratedFile[];
  disabled?: boolean;
}


// 检测是否为slide内容

// 导出选择模态框

const ExportButton: React.FC<ExportButtonProps> = ({ files, disabled = false }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // 筛选出slide类型的文件
  const slideFiles = files.filter(file =>
    file.status === 'completed' &&
    file.contentType === 'html' &&
    isSlideContent(file.content)
  );

  // 如果没有slide文件，不显示导出按钮
  if (slideFiles.length === 0) {
    return null;
  }

  const handleExport = async (type: 'pdf' | 'ppt') => {
    setIsExporting(true);
    setIsModalOpen(false);

    try {
      if (type === 'pdf') {
        await exportToPDF(slideFiles);
      } else {
        await exportToPPT(slideFiles);
      }
    } catch (error) {
      console.error('导出失败:', error);
      alert('导出失败，请查看控制台获取详细信息');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        disabled={disabled || isExporting}
        className="px-3 py-1.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:shadow-md transition-all duration-200 text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isExporting ? (
          <>
            <svg className="animate-spin h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            导出中...
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出
          </>
        )}
      </button>

      <ExportModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onExport={handleExport}
        slideFiles={slideFiles}
      />
    </>
  );
};

// PDF导出功能 - 使用Puppeteer API
const exportToPDF = async (slideFiles: GeneratedFile[]) => {
  try {
    console.log('开始PDF导出，文件数量:', slideFiles.length);

    if (slideFiles.length === 1) {
      // 单个文件导出
      const file = slideFiles[0];
      const filename = file.name.replace('.html', '.pdf');

      console.log('单文件导出:', filename);

      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          htmlContent: file.content,
          filename: filename,
          options: {
            format: 'A4',
            landscape: true,
            printBackground: true,
            margin: {
              top: '5mm',
              right: '5mm',
              bottom: '5mm',
              left: '5mm'
            }
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'PDF生成失败');
      }

      // 下载PDF文件
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log('单文件PDF导出成功');
    } else {
      // 多个文件合并导出
      const filename = `slides-presentation-${new Date().toISOString().slice(0, 10)}.pdf`;

      console.log('多文件导出:', filename, '文件数量:', slideFiles.length);

      // 使用PPT导出的HTML合并逻辑生成完整的HTML
      const combinedHTML = generatePDFHTML(slideFiles);

      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          htmlContent: combinedHTML,
          filename: filename,
          options: {
            format: 'A4',
            landscape: true,
            printBackground: true,
            margin: {
              top: '5mm',
              right: '5mm',
              bottom: '5mm',
              left: '5mm'
            }
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '多文件PDF生成失败');
      }

      // 下载PDF文件
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log('多文件PDF导出成功');
    }
  } catch (error) {
    console.error('PDF导出错误:', error);
    throw new Error(`PDF导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// PPT导出功能（生成PowerPoint兼容的HTML）
const exportToPPT = async (slideFiles: GeneratedFile[]) => {
  try {
    console.log('Starting PPT export process...');

    // 使用统一的PPT HTML生成函数
    const pptHtml = importedGeneratePPTHTML(slideFiles);

    // 创建Blob并触发下载
    const blob = new Blob([pptHtml], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `slides-presentation-${new Date().toISOString().slice(0, 10)}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('PPT export completed successfully');
  } catch (error) {
    console.error('Error during PPT export:', error);
    throw error; // 向上抛出错误以便外部函数处理
  }
};

export default ExportButton;
