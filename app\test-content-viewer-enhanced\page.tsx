"use client";

import React, { useState, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { ContentType, ViewMode } from '@/components/content-viewer/types';
import { GeneratedFile } from '@/app/content-generator/types';

// 导入导出功能组件
const ExportButton = dynamic(
  () => import('@/app/content-generator/components/export-button'),
  { ssr: false }
);

// 使用动态导入加载ContentViewer组件，并禁用SSR
const ContentViewer = dynamic(
  () => import('@/components/content-viewer/content-viewer'),
  { ssr: false }
);

// 幻灯片示例1 - 封面页
const slideExample1 = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>掘金AI的万亿美元机会</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .slide {
            width: 1280px;
            height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            font-family: 'Montserrat', sans-serif;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .logo {
            width: 120px;
            height: 120px;
            background-color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="logo">
            <i class="fas fa-seedling text-4xl text-green-600"></i>
        </div>
        
        <h1 class="text-6xl font-bold mb-6 gradient-text">掘金AI的万亿美元机会</h1>
        <h2 class="text-2xl text-gray-300 mb-12">红杉资本最新内部分享</h2>
        
        <div class="absolute bottom-8 text-gray-400">
            <p class="text-sm">AI Ascent大会 · 2024</p>
        </div>
    </div>
</body>
</html>`;

// 幻灯片示例2 - 内容页
const slideExample2 = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据飞轮与营收质量</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            font-family: 'Montserrat', sans-serif;
            position: relative;
            padding: 40px;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .feature-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            flex-shrink: 0;
        }
        .icon-bg-blue {
            background: linear-gradient(135deg, #38bdf8 0%, #0284c7 100%);
        }
        .icon-bg-purple {
            background: linear-gradient(135deg, #a78bfa 0%, #7c3aed 100%);
        }
        .icon-bg-pink {
            background: linear-gradient(135deg, #fb7185 0%, #e11d48 100%);
        }
        .cycle-diagram {
            position: relative;
            width: 300px;
            height: 300px;
            margin: 0 auto;
        }
        .cycle-step {
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            text-align: center;
            font-size: 12px;
        }
        .cycle-arrow {
            position: absolute;
            width: 30px;
            height: 30px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-8">
            <h1 class="text-4xl font-bold mb-2 gradient-text">数据飞轮与营收质量</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
        </div>
        
        <div class="grid grid-cols-12 gap-6">
            <div class="col-span-5 card">
                <div class="flex items-center mb-4">
                    <div class="feature-icon icon-bg-blue">
                        <i class="fas fa-cogs text-white"></i>
                    </div>
                    <h2 class="text-xl font-bold">数据飞轮概念</h2>
                </div>
                <div class="cycle-diagram">
                    <div class="cycle-step bg-blue-600/80" style="top: 110px; left: 10px;">
                        <i class="fas fa-users mb-1"></i>
                        <span>更多用户</span>
                    </div>
                    <div class="cycle-step bg-purple-600/80" style="top: 10px; left: 110px;">
                        <i class="fas fa-database mb-1"></i>
                        <span>更多数据</span>
                    </div>
                    <div class="cycle-step bg-pink-600/80" style="top: 110px; right: 10px;">
                        <i class="fas fa-brain mb-1"></i>
                        <span>更好模型</span>
                    </div>
                    <div class="cycle-step bg-indigo-600/80" style="bottom: 10px; left: 110px;">
                        <i class="fas fa-star mb-1"></i>
                        <span>更好产品</span>
                    </div>
                    <div class="cycle-arrow" style="top: 50px; left: 70px;"><i class="fas fa-arrow-right"></i></div>
                    <div class="cycle-arrow" style="top: 70px; right: 50px;"><i class="fas fa-arrow-down"></i></div>
                    <div class="cycle-arrow" style="bottom: 50px; right: 70px;"><i class="fas fa-arrow-left"></i></div>
                    <div class="cycle-arrow" style="bottom: 70px; left: 50px;"><i class="fas fa-arrow-up"></i></div>
                </div>
                <div class="mt-4 p-3 bg-blue-900/20 rounded-lg">
                    <p class="text-sm italic">"谁有数据飞轮？这个数据飞轮能推动哪些业务指标的提升？" — Pat Grady</p>
                </div>
            </div>
            
            <div class="col-span-7 card">
                <div class="flex items-center mb-4">
                    <div class="feature-icon icon-bg-purple">
                        <i class="fas fa-chart-bar text-white"></i>
                    </div>
                    <h2 class="text-xl font-bold">真实营收 vs 氛围营收</h2>
                </div>
                <div class="grid grid-cols-2 gap-6">
                    <div class="p-4 bg-purple-900/20 rounded-lg">
                        <h3 class="font-bold text-purple-300 mb-3 flex items-center">
                            <i class="fas fa-times-circle mr-2"></i> 氛围营收
                        </h3>
                        <ul class="text-sm space-y-2">
                            <li class="flex items-start">
                                <i class="fas fa-angle-right text-purple-300 mt-1 mr-2"></i>
                                <span>客户只是试水，没有真实行为改变</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-angle-right text-purple-300 mt-1 mr-2"></i>
                                <span>采用率、参与度和留存率低</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-angle-right text-purple-300 mt-1 mr-2"></i>
                                <span>感觉增长快但实际不可持续</span>
                            </li>
                        </ul>
                        <div class="mt-4 p-2 bg-gray-800 rounded">
                            <p class="text-xs italic">"别自欺欺人，以为有了真实营收，结果只是'氛围营收'"</p>
                        </div>
                    </div>
                    <div class="p-4 bg-blue-900/20 rounded-lg">
                        <h3 class="font-bold text-blue-300 mb-3 flex items-center">
                            <i class="fas fa-check-circle mr-2"></i> 真实营收
                        </h3>
                        <ul class="text-sm space-y-2">
                            <li class="flex items-start">
                                <i class="fas fa-angle-right text-blue-300 mt-1 mr-2"></i>
                                <span>客户真正依赖产品解决问题</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-angle-right text-blue-300 mt-1 mr-2"></i>
                                <span>高参与度和持续使用</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-angle-right text-blue-300 mt-1 mr-2"></i>
                                <span>基于信任的长期关系</span>
                            </li>
                        </ul>
                        <div class="mt-4 p-2 bg-gray-800 rounded">
                            <p class="text-xs italic">"在当前阶段，信任比产品更重要"</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6">
                    <div class="flex items-center mb-4">
                        <div class="feature-icon icon-bg-pink">
                            <i class="fas fa-percentage text-white"></i>
                        </div>
                        <h2 class="text-xl font-bold">毛利率路径</h2>
                    </div>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="p-3 bg-pink-900/20 rounded-lg">
                            <h3 class="font-bold text-pink-300 text-sm mb-1">成本下降</h3>
                            <p class="text-xs">过去18个月，每个token成本下降99%</p>
                        </div>
                        <div class="p-3 bg-pink-900/20 rounded-lg">
                            <h3 class="font-bold text-pink-300 text-sm mb-1">价值上升</h3>
                            <p class="text-xs">从销售工具转向销售成果</p>
                        </div>
                        <div class="p-3 bg-pink-900/20 rounded-lg">
                            <h3 class="font-bold text-pink-300 text-sm mb-1">清晰路径</h3>
                            <p class="text-xs">通向健康毛利率的可行计划</p>
                        </div>
                    </div>
                    <div class="mt-4 h-40">
                        <canvas id="marginChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="absolute bottom-4 right-6 text-xs text-gray-400">红杉资本 AI Ascent大会 2024</div>
    </div>
    
    <script>
        // 毛利率路径图表
        const marginCtx = document.getElementById('marginChart').getContext('2d');
        new Chart(marginCtx, {
            type: 'line',
            data: {
                labels: ['现在', '6个月', '12个月', '18个月'],
                datasets: [{
                    label: '毛利率(%)',
                    data: [20, 35, 50, 65],
                    borderColor: 'rgba(251, 113, 133, 1)',
                    backgroundColor: 'rgba(251, 113, 133, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: 'white'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>`;

// 幻灯片示例3 - 结束页
const slideExample3 = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户参与度突破</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            font-family: 'Montserrat', sans-serif;
            position: relative;
            padding: 40px;
        }
        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .glow {
            position: absolute;
            border-radius: 50%;
        }
        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }
        .feature-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            flex-shrink: 0;
        }
        .icon-bg-blue {
            background: linear-gradient(135deg, #38bdf8 0%, #0284c7 100%);
        }
        .icon-bg-purple {
            background: linear-gradient(135deg, #a78bfa 0%, #7c3aed 100%);
        }
        .icon-bg-pink {
            background: linear-gradient(135deg, #fb7185 0%, #e11d48 100%);
        }
        .voice-demo {
            position: relative;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            overflow: hidden;
            margin-top: 15px;
        }
        .voice-wave {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: repeating-linear-gradient(
                to right,
                transparent,
                transparent 10px,
                rgba(167, 139, 250, 0.3) 10px,
                rgba(167, 139, 250, 0.3) 20px
            );
            animation: wave 2s linear infinite;
        }
        @keyframes wave {
            0% { transform: translateX(0); }
            100% { transform: translateX(-20px); }
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <div class="mb-8">
            <h1 class="text-4xl font-bold mb-2 gradient-text">从炒作到真实价值：用户参与度突破</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
        </div>
        
        <div class="grid grid-cols-12 gap-6">
            <div class="col-span-4 card">
                <div class="flex items-center mb-4">
                    <div class="feature-icon icon-bg-blue">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                    <h2 class="text-xl font-bold">DAU/MAU显著提升</h2>
                </div>
                <div class="h-48 mb-4">
                    <canvas id="engagementChart"></canvas>
                </div>
                <p class="text-sm">"ChatGPT的日活/月活比例已接近Reddit水平，意味着人们正从AI中获得真实价值" — Sonya Huang</p>
            </div>
            
            <div class="col-span-4 card">
                <div class="flex items-center mb-4">
                    <div class="feature-icon icon-bg-purple">
                        <i class="fas fa-microphone-alt text-white"></i>
                    </div>
                    <h2 class="text-xl font-bold">语音生成突破</h2>
                </div>
                <p class="mb-3">语音领域的"《她》时刻"已经到来，完全跨越"恐怖谷"</p>
                <div class="voice-demo">
                    <div class="voice-wave"></div>
                </div>
                <div class="mt-4 flex items-center">
                    <div class="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                        <i class="fas fa-play text-white"></i>
                    </div>
                    <div>
                        <p class="text-sm font-bold">AI语音示例</p>
                        <p class="text-xs text-gray-400">声音自然度接近人类水平</p>
                    </div>
                </div>
                <p class="mt-4 text-sm italic">"科幻与现实之间的鸿沟正在以惊人速度弥合"</p>
            </div>
            
            <div class="col-span-4 card">
                <div class="flex items-center mb-4">
                    <div class="feature-icon icon-bg-pink">
                        <i class="fas fa-code text-white"></i>
                    </div>
                    <h2 class="text-xl font-bold">编程领域爆发</h2>
                </div>
                <div class="mb-4 p-3 bg-pink-900/20 rounded-lg">
                    <h3 class="font-bold text-pink-300 mb-1">"尖叫级"产品市场契合度</h3>
                    <p class="text-sm">Claude 3.5 Sonnet发布后编程领域发生迅速"氛围转变"</p>
                </div>
                <ul class="space-y-3 text-sm">
                    <li class="flex items-start">
                        <i class="fas fa-angle-right text-pink-300 mt-1 mr-2"></i>
                        <span>经验丰富的"十倍效能"工程师效率大幅提升</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-angle-right text-pink-300 mt-1 mr-2"></i>
                        <span>完全不懂编程的人也能创造软件</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-angle-right text-pink-300 mt-1 mr-2"></i>
                        <span>有人用"vibe coding"制作了DocSend替代品</span>
                    </li>
                </ul>
            </div>
            
            <div class="col-span-12 card mt-4">
                <div class="flex items-center mb-4">
                    <div class="feature-icon icon-bg-blue">
                        <i class="fas fa-industry text-white"></i>
                    </div>
                    <h2 class="text-xl font-bold">深度应用案例</h2>
                </div>
                <div class="grid grid-cols-3 gap-4">
                    <div class="p-4 bg-blue-900/20 rounded-lg">
                        <h3 class="font-bold text-blue-300 mb-2 flex items-center">
                            <i class="fas fa-ad mr-2"></i> 广告领域
                        </h3>
                        <p class="text-sm">创作精准美观的广告文案，大幅提升转化率</p>
                    </div>
                    <div class="p-4 bg-purple-900/20 rounded-lg">
                        <h3 class="font-bold text-purple-300 mb-2 flex items-center">
                            <i class="fas fa-graduation-cap mr-2"></i> 教育领域
                        </h3>
                        <p class="text-sm">即时可视化新概念，个性化学习体验</p>
                    </div>
                    <div class="p-4 bg-pink-900/20 rounded-lg">
                        <h3 class="font-bold text-pink-300 mb-2 flex items-center">
                            <i class="fas fa-heartbeat mr-2"></i> 医疗健康
                        </h3>
                        <p class="text-sm">OpenEvidence等应用能更好地辅助诊断</p>
                    </div>
                </div>
                <div class="mt-4 p-3 bg-gray-800 rounded-lg">
                    <p class="italic">"我们才刚刚触及可能性的冰山一角" — Sonya Huang</p>
                </div>
            </div>
        </div>
        
        <div class="absolute bottom-4 right-6 text-xs text-gray-400">红杉资本 AI Ascent大会 2024</div>
    </div>
    
    <script>
        // 用户参与度图表
        const engagementCtx = document.getElementById('engagementChart').getContext('2d');
        new Chart(engagementCtx, {
            type: 'line',
            data: {
                labels: ['2023 Q1', '2023 Q2', '2023 Q3', '2023 Q4', '2024 Q1'],
                datasets: [
                    {
                        label: 'ChatGPT DAU/MAU',
                        data: [15, 25, 40, 55, 68],
                        borderColor: 'rgba(56, 189, 248, 1)',
                        backgroundColor: 'rgba(56, 189, 248, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.3
                    },
                    {
                        label: 'Reddit DAU/MAU',
                        data: [65, 66, 67, 68, 69],
                        borderColor: 'rgba(251, 113, 133, 1)',
                        backgroundColor: 'rgba(251, 113, 133, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.3
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            color: 'white',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: 'white'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
`;

export default function TestContentViewerEnhancedPage() {
  const [contentType, setContentType] = useState<ContentType>('html');
  const [viewMode, setViewMode] = useState<ViewMode>('split');
  const [currentExample, setCurrentExample] = useState<string>('basic');

  const htmlExample = `<!DOCTYPE html>
<html>
<head>
  <title>HTML内容查看器示例</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      color: #333;
      background-color: #f9f9f9;
    }
    h1 {
      color: #2563eb;
      border-bottom: 2px solid #e5e7eb;
      padding-bottom: 10px;
      text-align: center;
    }
    h2 {
      color: #4b5563;
      margin-top: 1.5em;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 20px;
    }
    .card {
      flex: 1;
      min-width: 200px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      background-color: white;
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    .card h3 {
      margin-top: 0;
      color: #4b5563;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 8px;
    }
    .highlight {
      background-color: #ffffcc;
      padding: 2px 5px;
      border-radius: 3px;
    }
    .demo-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    .demo-table th, .demo-table td {
      border: 1px solid #e5e7eb;
      padding: 8px 12px;
      text-align: left;
    }
    .demo-table th {
      background-color: #f3f4f6;
    }
    .demo-table tr:nth-child(even) {
      background-color: #f9fafb;
    }
    .demo-form {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      margin: 20px 0;
    }
    .form-group {
      margin-bottom: 15px;
    }
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    .form-group input, .form-group textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .btn {
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    .btn:hover {
      background-color: #1d4ed8;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #6b7280;
      font-size: 0.9em;
      border-top: 1px solid #e5e7eb;
      padding-top: 20px;
    }
  </style>
</head>
<body>
  <h1>HTML内容查看器示例</h1>

  <p>这是一个展示<span class="highlight">HTML内容查看器</span>功能的示例页面。它包含了各种HTML元素和CSS样式，用于测试渲染效果。</p>

  <h2>功能特点</h2>

  <div class="container">
    <div class="card">
      <h3>代码高亮</h3>
      <p>支持HTML代码的语法高亮显示，使代码更易读。</p>
    </div>
    <div class="card">
      <h3>实时预览</h3>
      <p>实时渲染HTML内容，显示最终效果。</p>
    </div>
    <div class="card">
      <h3>分屏模式</h3>
      <p>同时查看代码和预览，并支持调整分割比例。</p>
    </div>
  </div>

  <h2>数据表格示例</h2>

  <table class="demo-table">
    <thead>
      <tr>
        <th>功能</th>
        <th>描述</th>
        <th>支持状态</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>HTML渲染</td>
        <td>支持完整的HTML标签和CSS样式</td>
        <td>✅ 已支持</td>
      </tr>
      <tr>
        <td>代码高亮</td>
        <td>支持HTML代码语法高亮</td>
        <td>✅ 已支持</td>
      </tr>
      <tr>
        <td>分屏模式</td>
        <td>同时显示代码和预览</td>
        <td>✅ 已支持</td>
      </tr>
    </tbody>
  </table>

  <h2>表单示例</h2>

  <div class="demo-form">
    <div class="form-group">
      <label for="name">姓名</label>
      <input type="text" id="name" placeholder="请输入您的姓名">
    </div>

    <div class="form-group">
      <label for="email">电子邮箱</label>
      <input type="email" id="email" placeholder="请输入您的电子邮箱">
    </div>

    <div class="form-group">
      <label for="message">留言</label>
      <textarea id="message" rows="4" placeholder="请输入您的留言"></textarea>
    </div>

    <button class="btn">提交</button>
  </div>

  <footer>
    <p>&copy; 2023 HTML内容查看器示例 | 所有内容仅用于测试</p>
  </footer>
</body>
</html>`;

  const markdownExample = `# Markdown内容查看器示例

这是一个用于测试 **Markdown内容查看器** 功能的示例文档。本文档展示了Markdown的各种语法和渲染效果。

## 📓 标题和段落

Markdown支持多级标题，从一级标题（#）到六级标题（######）。

### 这是三级标题

#### 这是四级标题

段落之间需要有空行分隔。这样才能确保每个段落独立渲染。

这是另一个段落，展示段落分隔效果。

## 🖊️ 文本格式化

Markdown支持各种文本格式化方式：

- **粗体文本** - 使用双星号包围
- *斜体文本* - 使用单星号包围
- ***粗斜体文本*** - 使用三星号包围
- ~~删除线文本~~ - 使用双波浪线包围
- \`行内代码\` - 使用反引号包围
- <u>下划线文本</u> - 使用HTML标签
- <mark>高亮文本</mark> - 使用HTML标签
- H<sub>2</sub>O - 下标使用HTML标签
- X<sup>2</sup> - 上标使用HTML标签

## 📌 列表

### 无序列表

- 项目一
- 项目二
  - 嵌套项目 2.1
  - 嵌套项目 2.2
- 项目三

### 有序列表

1. 第一步
2. 第二步
   1. 嵌套步骤 2.1
   2. 嵌套步骤 2.2
3. 第三步

### 任务列表

- [x] 已完成任务
- [ ] 未完成任务
- [x] 另一个已完成任务

## 💻 代码块

行内代码示例：\`const greeting = "Hello World";\`

多行代码块示例：

\`\`\`javascript
function greeting(name) {
  return \`Hello, \${name}!\`;
}

console.log(greeting('World')); // 输出: Hello, World!
\`\`\`

Python代码示例：

\`\`\`python
def greeting(name):
    return f"Hello, {name}!"

print(greeting("World"))  # 输出: Hello, World!
\`\`\`

HTML代码示例：

\`\`\`html
<!DOCTYPE html>
<html>
<head>
  <title>示例</title>
</head>
<body>
  <h1>Hello World</h1>
</body>
</html>
\`\`\`

## 📃 表格

| 功能 | 描述 | 支持状态 |
|:------|:------:|------:|
| Markdown渲染 | 支持各种Markdown语法 | ✅ 已支持 |
| 代码高亮 | 支持多种语言的代码高亮 | ✅ 已支持 |
| 分屏模式 | 同时显示代码和预览 | ✅ 已支持 |

> 注意：表格中的列可以左对齐、居中或右对齐。

## 🔗 链接和图片

### 链接

- [外部链接到Markdown指南](https://www.markdownguide.org/)
- [带标题的链接](https://github.com/ "GitHub首页")
- [引用式链接][1]

[1]: https://www.markdownguide.org/basic-syntax/ "Markdown基本语法"

### 图片

![Markdown图标](https://markdown-here.com/img/icon256.png)

## 💬 引用和注释

> Markdown是一种轻量级标记语言，创建于2004年，现在已经成为世界上最流行的标记语言之一。
>
> 它的设计目标是易读易写，并且可以轻松转换为HTML。

## 🔲 水平线和分隔线

下面是一条水平线：

---

## 📚 其他Markdown扩展语法

### 定义列表

Markdown
: 一种轻量级标记语言

HTML
: 超文本标记语言

### 脚注

这是一个带有脚注的文本[^1]。

[^1]: 这是脚注的内容。

### 缩写

HTML 是一种标记语言。

*[HTML]: 超文本标记语言

## 🌐 特殊符号和表情

特殊符号：&copy; &reg; &trade; &euro; &yen; &delta; &Delta; &alpha; &omega;

表情符号：😀 😁 😂 😃 😄 🚀 👍 ❤️

---

感谢使用 **Markdown内容查看器**！如果您有任何问题或建议，请随时联系我们。`;

  // 获取当前示例内容
  const getCurrentContent = () => {
    if (contentType === 'markdown') {
      return markdownExample;
    }

    switch (currentExample) {
      case 'slide1':
        return slideExample1;
      case 'slide2':
        return slideExample2;
      case 'slide3':
        return slideExample3;
      default:
        return htmlExample;
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">增强版内容查看器示例</h1>

      {/* 内容类型切换按钮 */}
      <div className="flex space-x-4 mb-4">
        <button
          className={`px-4 py-2 rounded-md ${contentType === 'html' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => {
            setContentType('html');
            setCurrentExample('basic');
          }}
        >
          HTML示例
        </button>
        <button
          className={`px-4 py-2 rounded-md ${contentType === 'markdown' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setContentType('markdown')}
        >
          Markdown示例
        </button>
      </div>

      {/* HTML示例选择按钮 */}
      {contentType === 'html' && (
        <div className="flex space-x-2 mb-4">
          <button
            className={`px-3 py-1.5 text-sm rounded-md ${currentExample === 'basic' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setCurrentExample('basic')}
          >
            基础HTML
          </button>
          <button
            className={`px-3 py-1.5 text-sm rounded-md ${currentExample === 'slide1' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setCurrentExample('slide1')}
          >
            幻灯片1 - 封面
          </button>
          <button
            className={`px-3 py-1.5 text-sm rounded-md ${currentExample === 'slide2' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setCurrentExample('slide2')}
          >
            幻灯片2 - 内容
          </button>
          <button
            className={`px-3 py-1.5 text-sm rounded-md ${currentExample === 'slide3' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            onClick={() => setCurrentExample('slide3')}
          >
            幻灯片3 - 结束
          </button>
        </div>
      )}

      {/* 视图模式切换按钮 */}
      <div className="flex space-x-4 mb-4">
        <button
          className={`px-4 py-2 rounded-md ${viewMode === 'code' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setViewMode('code')}
        >
          仅代码
        </button>
        <button
          className={`px-4 py-2 rounded-md ${viewMode === 'preview' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setViewMode('preview')}
        >
          仅预览
        </button>
        <button
          className={`px-4 py-2 rounded-md ${viewMode === 'split' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setViewMode('split')}
        >
          分屏模式
        </button>
      </div>

      {/* 当前内容类型和视图模式信息 */}
      <div className="mb-4 p-3 bg-gray-100 rounded-md">
        <p className="text-gray-700">
          当前内容类型: <span className="font-semibold">{contentType === 'html' ? 'HTML' : 'Markdown'}</span> |
          当前视图模式: <span className="font-semibold">
            {viewMode === 'code' ? '仅代码' : viewMode === 'preview' ? '仅预览' : '分屏模式'}
          </span>
          {contentType === 'html' && (
            <span> | 当前示例: <span className="font-semibold">
              {currentExample === 'basic' ? '基础HTML' :
               currentExample === 'slide1' ? '幻灯片1 - 封面' :
               currentExample === 'slide2' ? '幻灯片2 - 内容' :
               currentExample === 'slide3' ? '幻灯片3 - 结束' : '未知'}
            </span></span>
          )}
        </p>
        {currentExample.startsWith('slide') && (
          <div className="mt-2 p-2 bg-purple-50 border border-purple-200 rounded">
            <p className="text-purple-700 text-sm">
              <i className="fas fa-info-circle mr-1"></i>
              这是幻灯片示例，包含 <code>class="slide"</code> 属性，可以测试导出功能。
              {currentExample === 'slide1' && '这是封面页示例。'}
              {currentExample === 'slide2' && '这是内容页示例，展示了导出功能的特性。'}
              {currentExample === 'slide3' && '这是结束页示例，包含测试说明。'}
            </p>
          </div>
        )}
      </div>

      <div className="h-[600px] border rounded-lg overflow-hidden">
        <ContentViewer
          content={getCurrentContent()}
          contentType={contentType}
          initialViewMode={viewMode}
          onContentTypeChange={setContentType}
          onViewModeChange={setViewMode}
        />
      </div>

      {/* 导出功能测试说明 */}
      {currentExample.startsWith('slide') && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">
            <i className="fas fa-download mr-2"></i>
            导出功能测试说明
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white p-3 rounded border">
              <h4 className="font-semibold text-gray-800 mb-2">
                <i className="fas fa-file-pdf text-red-500 mr-1"></i>
                PDF导出测试
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 在内容生成器中生成多个幻灯片文件</li>
                <li>• 点击"导出"按钮选择PDF格式</li>
                <li>• 验证多页自动合并功能</li>
                <li>• 检查样式和排版是否保持完整</li>
              </ul>
            </div>
            <div className="bg-white p-3 rounded border">
              <h4 className="font-semibold text-gray-800 mb-2">
                <i className="fas fa-presentation text-purple-500 mr-1"></i>
                PPT导出测试
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 在内容生成器中生成多个幻灯片文件</li>
                <li>• 点击"导出"按钮选择PPT格式</li>
                <li>• 测试鼠标滚轮导航功能</li>
                <li>• 验证键盘导航和全屏模式</li>
              </ul>
            </div>
          </div>
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <p className="text-sm text-yellow-800">
              <i className="fas fa-lightbulb mr-1"></i>
              <strong>提示：</strong>要测试完整的导出功能，请访问
              <a href="/content-generator-stream" className="text-blue-600 hover:underline mx-1">内容生成器页面</a>
              生成包含多个幻灯片的内容，然后使用导出功能。
            </p>
          </div>
        </div>
      )}

      {/* 导出功能实际测试区域 */}
      {currentExample.startsWith('slide') && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800 mb-3">
            <i className="fas fa-flask mr-2"></i>
            导出功能实际测试
          </h3>
          <p className="text-sm text-green-700 mb-4">
            下面的导出按钮使用真实的导出功能，您可以直接测试当前显示的幻灯片内容的导出效果。
          </p>
          <div className="space-y-4">
            <ExportTestComponent
              slideContent={getCurrentContent()}
              slideName={`test-slide-${currentExample}`}
            />
            <MultiSlideExportTest />
          </div>
        </div>
      )}
    </div>
  );
}

// 导出测试组件
interface ExportTestComponentProps {
  slideContent: string;
  slideName: string;
}

const ExportTestComponent: React.FC<ExportTestComponentProps> = ({ slideContent, slideName }) => {
  // 创建模拟的GeneratedFile数组用于测试
  const testFiles = useMemo((): GeneratedFile[] => [
    {
      id: `test-${Date.now()}`,
      name: `${slideName}.html`,
      content: slideContent,
      contentType: 'html',
      status: 'completed',
      viewMode: 'preview',
      timestamp: Date.now()
    }
  ], [slideContent, slideName]);

  return (
    <div className="flex items-center justify-between p-3 bg-white rounded border">
      <div>
        <h4 className="font-semibold text-gray-800">当前幻灯片导出测试</h4>
        <p className="text-sm text-gray-600">
          文件名: {slideName}.html |
          状态: <span className="text-green-600">已完成</span> |
          类型: <span className="text-blue-600">HTML幻灯片</span>
        </p>
      </div>
      <div className="flex items-center gap-2">
        <ExportButton
          files={testFiles}
          disabled={false}
        />
        <div className="text-xs text-gray-500">
          <i className="fas fa-info-circle mr-1"></i>
          单文件测试
        </div>
      </div>
    </div>
  );
};

// 多文件导出测试组件
const MultiSlideExportTest: React.FC = () => {
  // 创建包含所有三个幻灯片的测试文件数组
  const multiTestFiles = useMemo((): GeneratedFile[] => [
    {
      id: `test-multi-1-${Date.now()}`,
      name: 'test-slide-1-cover.html',
      content: slideExample1,
      contentType: 'html',
      status: 'completed',
      viewMode: 'preview',
      timestamp: Date.now()
    },
    {
      id: `test-multi-2-${Date.now()}`,
      name: 'test-slide-2-content.html',
      content: slideExample2,
      contentType: 'html',
      status: 'completed',
      viewMode: 'preview',
      timestamp: Date.now() + 1
    },
    {
      id: `test-multi-3-${Date.now()}`,
      name: 'test-slide-3-ending.html',
      content: slideExample3,
      contentType: 'html',
      status: 'completed',
      viewMode: 'preview',
      timestamp: Date.now() + 2
    }
  ], []);

  return (
    <div className="p-3 bg-white rounded border border-dashed border-green-300">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-semibold text-gray-800">多页幻灯片导出测试</h4>
          <p className="text-sm text-gray-600">
            包含3个幻灯片文件 |
            状态: <span className="text-green-600">全部已完成</span> |
            类型: <span className="text-blue-600">HTML幻灯片集合</span>
          </p>
          <div className="mt-2 text-xs text-gray-500">
            <div>• test-slide-1-cover.html (封面页)</div>
            <div>• test-slide-2-content.html (内容页)</div>
            <div>• test-slide-3-ending.html (结束页)</div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <ExportButton
            files={multiTestFiles}
            disabled={false}
          />
          <div className="text-xs text-gray-500">
            <i className="fas fa-layer-group mr-1"></i>
            多文件测试
          </div>
        </div>
      </div>
    </div>
  );
};
