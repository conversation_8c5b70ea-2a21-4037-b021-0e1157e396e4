// lib/export-utils.ts
import { GeneratedFile } from '../app/content-generator/types';

/**
 * JavaScript内容类型定义
 */
type JSContentType = {
  name: string;          // 内容类型名称
  detect: (html: string) => boolean;  // 检测函数
  libraries?: string[];  // 需要的外部库
  styles?: string;       // 需要的样式
  script?: string;       // 需要的脚本
  waitTime?: number;     // 建议等待时间(ms)
};

/**
 * 已知JavaScript内容类型注册表
 */
const jsContentTypes: JSContentType[] = [
  // Chart.js图表
  {
    name: 'chart.js',
    detect: (html: string) => {
      return html.includes('Chart.js') ||
             html.includes('chart.js') ||
             html.includes('new Chart(') ||
             html.includes('chart-container');
    },
    libraries: [
      'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js'
    ],
    styles: `
      canvas {
        display: block !important;
        visibility: visible !important;
        width: 100% !important;
        min-height: 200px !important;
      }
      .chart-container {
        display: block !important;
        visibility: visible !important;
        height: 450px !important;
        width: 100% !important;
        min-height: 300px !important;
      }
    `,
    script: `
      // 确保所有Chart.js相关元素可见
      document.querySelectorAll('canvas').forEach(function(canvas) {
        canvas.style.display = 'block';
        canvas.style.visibility = 'visible';
        canvas.style.width = '100%';
        canvas.style.minHeight = '200px';
      });

      document.querySelectorAll('.chart-container').forEach(function(container) {
        container.style.display = 'block';
        container.style.visibility = 'visible';
        container.style.height = '450px';
        container.style.width = '100%';
        container.style.minHeight = '300px';
      });
    `,
    waitTime: 2000
  },

  // ECharts图表
  {
    name: 'echarts',
    detect: (html: string) => {
      return html.includes('echarts') ||
             html.includes('ECharts') ||
             html.includes('initChart') ||
             html.includes('echarts.init');
    },
    libraries: [
      'https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js'
    ],
    styles: `
      .echarts-container, [id*='echarts'], [class*='echarts'] {
        display: block !important;
        visibility: visible !important;
        height: 450px !important;
        width: 100% !important;
        min-height: 300px !important;
      }
    `,
    script: `
      // 确保所有ECharts相关元素可见并重新初始化
      document.querySelectorAll('.echarts-container, [id*="echarts"], [class*="echarts"]').forEach(function(container) {
        container.style.display = 'block';
        container.style.visibility = 'visible';
        container.style.height = '450px';
        container.style.width = '100%';
        container.style.minHeight = '300px';
      });
    `,
    waitTime: 2000
  },

  // Highcharts图表
  {
    name: 'highcharts',
    detect: (html: string) => {
      return html.includes('Highcharts') ||
             html.includes('highcharts');
    },
    libraries: [
      'https://cdn.jsdelivr.net/npm/highcharts@10.3.3/highcharts.js'
    ],
    waitTime: 2000
  },

  // Plotly图表
  {
    name: 'plotly',
    detect: (html: string) => {
      return html.includes('Plotly') ||
             html.includes('plotly');
    },
    libraries: [
      'https://cdn.jsdelivr.net/npm/plotly.js@2.24.1/dist/plotly.min.js'
    ],
    waitTime: 2000
  },

  // Mermaid图表
  {
    name: 'mermaid',
    detect: (html: string) => {
      return html.includes('mermaid') ||
             html.includes('class="mermaid"') ||
             html.includes('class=\'mermaid\'');
    },
    libraries: [
      'https://cdn.jsdelivr.net/npm/mermaid@10.2.3/dist/mermaid.min.js'
    ],
    script: `
      // 初始化Mermaid
      if (typeof mermaid !== 'undefined') {
        mermaid.initialize({ startOnLoad: true });
      }
    `,
    waitTime: 2000
  },

  // D3.js可视化
  {
    name: 'd3',
    detect: (html: string) => {
      return html.includes('d3.js') ||
             html.includes('d3.select') ||
             html.includes('d3.scale');
    },
    libraries: [
      'https://cdn.jsdelivr.net/npm/d3@7.8.5/dist/d3.min.js'
    ],
    waitTime: 2000
  },

  // 通用动画
  {
    name: 'animation',
    detect: (html: string) => {
      return html.includes('animation') ||
             html.includes('animate') ||
             html.includes('transition') ||
             html.includes('@keyframes');
    },
    waitTime: 1500
  },

  // 通用JavaScript
  {
    name: 'general-js',
    detect: (html: string) => {
      return html.includes('<script') ||
             html.includes('function') ||
             html.includes('addEventListener') ||
             html.includes('document.querySelector');
    },
    waitTime: 1000
  }
];

/**
 * 检测HTML内容中包含的JavaScript内容类型
 */
export function detectJSContentTypes(html: string): JSContentType[] {
  return jsContentTypes.filter(type => type.detect(html));
}

/**
 * 获取HTML内容所需的所有外部库
 */
export function getRequiredLibraries(html: string): string[] {
  const types = detectJSContentTypes(html);
  const libraries = new Set<string>();

  types.forEach(type => {
    if (type.libraries) {
      type.libraries.forEach(lib => libraries.add(lib));
    }
  });

  return Array.from(libraries);
}

/**
 * 获取HTML内容所需的所有样式
 */
export function getRequiredStyles(html: string): string {
  const types = detectJSContentTypes(html);
  return types
    .filter(type => type.styles)
    .map(type => type.styles)
    .join('\n');
}

/**
 * 获取HTML内容所需的所有脚本
 */
export function getRequiredScripts(html: string): string {
  const types = detectJSContentTypes(html);
  return types
    .filter(type => type.script)
    .map(type => type.script)
    .join('\n');
}

/**
 * 获取HTML内容的建议等待时间
 */
export function getSuggestedWaitTime(html: string): number {
  const types = detectJSContentTypes(html);
  if (types.length === 0) return 0;

  // 取最大等待时间
  return Math.max(...types.map(type => type.waitTime || 0));
}

/**
 * 判断内容是否为幻灯片
 */
export function isSlideContent(content: string): boolean {
  return content.includes('<div class="slide"') || content.includes('class="slide');
}

/**
 * 生成专门用于PDF的HTML（基于PPT导出逻辑，但去掉导航元素）
 */
export function generatePDFHTML(slideFiles: GeneratedFile[]): string {
  let allStyles = new Set<string>();
  let allLinks = new Set<string>();
  let allScripts = new Set<string>();
  let allExternalScripts = new Set<string>();

  let combinedHtmlContent = '';

  const slideContents = slideFiles.map((file, index) => {
    let slideContent = '';

    if (file.content.includes('<!DOCTYPE') || file.content.includes('<html')) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(file.content, 'text/html');

      // 收集样式
      const styles = doc.head?.querySelectorAll('style');
      styles?.forEach(style => {
        if (style.textContent) {
          allStyles.add(style.textContent);
        }
      });

      // 收集外部链接
      const links = doc.head?.querySelectorAll('link[rel="stylesheet"]');
      links?.forEach(link => {
        const href = (link as HTMLLinkElement).href;
        if (href) {
          allLinks.add(href);
        }
      });

      // 收集内联脚本
      const scripts = doc.querySelectorAll('script:not([src])');
      scripts.forEach(script => {
        if (script.textContent) {
          allScripts.add(script.textContent);
        }
      });

      // 收集外部脚本
      const externalScripts = doc.querySelectorAll('script[src]');
      externalScripts.forEach(script => {
        const src = script.getAttribute('src');
        if (src) {
          if (src.startsWith('http') || src.startsWith('//')) {
            allExternalScripts.add(src);
          } else {
            try {
              const absoluteUrl = new URL(src, window.location.href).href;
              allExternalScripts.add(absoluteUrl);
            } catch (e) {
              allExternalScripts.add(src);
            }
          }
        }
      });

      // 直接使用完整的body内容
      slideContent = doc.body?.innerHTML || file.content;
    } else {
      slideContent = file.content;
    }

    // 添加到组合HTML内容中，用于检测JS内容类型
    combinedHtmlContent += file.content;

    return `
    <div class="slide-page" data-slide="${index + 1}" id="slide-${index + 1}">
      ${slideContent}
    </div>`;
  }).join('\n');

  // 获取所需的外部库
  const requiredLibraries = getRequiredLibraries(combinedHtmlContent);
  const libraryTags = requiredLibraries.map(lib =>
    `<script src="${lib}"></script>`
  ).join('\n    ');

  // 获取所需的样式
  const requiredStyles = getRequiredStyles(combinedHtmlContent);

  // 获取所需的脚本
  const requiredScripts = getRequiredScripts(combinedHtmlContent);

  // 原始样式和外部链接
  const combinedStyles = Array.from(allStyles).join('\n');
  const linkTags = Array.from(allLinks).map(href =>
    `<link href="${href}" rel="stylesheet">`
  ).join('\n    ');

  // 添加外部脚本标签
  const externalScriptTags = Array.from(allExternalScripts).map(src =>
    `<script src="${src}"></script>`
  ).join('\n    ');

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示文稿 - ${slideFiles.length} 页</title>
    ${linkTags}

    <!-- 自动检测到的必要库 -->
    ${libraryTags}

    <!-- 原始外部脚本 -->
    ${externalScriptTags}

    <style>
        @page {
            size: 1280px 720px;
            margin: 0;
        }

        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: white;
            width: 1280px;
        }

        .slide-page {
            width: 1280px;
            height: 720px;
            margin: 0;
            background: white;
            page-break-after: always;
            page-break-inside: avoid;
            position: relative;
            overflow: hidden;
            display: block;
        }

        .slide-page:last-child {
            page-break-after: auto;
        }

        .slide-page .slide {
            width: 1280px !important;
            height: 720px !important;
            margin: 0 !important;
            position: relative !important;
            transform: none !important;
            overflow: visible !important;
            display: block !important;
            padding: 40px !important;
            box-sizing: border-box !important;
        }

        /* 强制应用原始样式 - 放在前面 */
        ${combinedStyles}

        /* 然后应用修复样式 - 确保优先级 */
        .slide-page .logo {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 auto 30px auto !important;
            width: 120px !important;
            height: 120px !important;
            background-color: white !important;
            border-radius: 50% !important;
        }

        .slide-page .feature-icon {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-shrink: 0 !important;
            width: 36px !important;
            height: 36px !important;
            border-radius: 50% !important;
            margin-right: 8px !important;
        }

        /* 确保标题正确显示 */
        .slide-page h1 {
            display: block !important;
            text-align: center !important;
            font-size: 3.75rem !important; /* text-6xl */
            font-weight: 700 !important;
            margin-bottom: 1.5rem !important;
            line-height: 1 !important;
        }

        .slide-page h2 {
            display: block !important;
            font-size: 1.5rem !important; /* text-2xl */
            margin-bottom: 3rem !important;
            text-align: center !important;
        }

        .slide-page h3 {
            display: block !important;
            font-size: 1.25rem !important; /* text-xl */
            font-weight: 700 !important;
        }

        /* 确保网格布局正确 - 使用具体的CSS Grid属性 */
        .slide-page .grid {
            display: grid !important;
        }

        .slide-page .grid-cols-12 {
            grid-template-columns: repeat(12, minmax(0, 1fr)) !important;
        }

        .slide-page .grid-cols-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        }

        .slide-page .grid-cols-3 {
            grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
        }

        .slide-page .col-span-4 {
            grid-column: span 4 / span 4 !important;
        }

        .slide-page .col-span-5 {
            grid-column: span 5 / span 5 !important;
        }

        .slide-page .col-span-7 {
            grid-column: span 7 / span 7 !important;
        }

        .slide-page .gap-6 {
            gap: 1.5rem !important;
        }

        .slide-page .gap-4 {
            gap: 1rem !important;
        }

        /* 确保弹性布局正确 */
        .slide-page .flex {
            display: flex !important;
        }

        .slide-page .flex-col {
            flex-direction: column !important;
        }

        .slide-page .items-center {
            align-items: center !important;
        }

        .slide-page .justify-center {
            justify-content: center !important;
        }

        .slide-page .text-center {
            text-align: center !important;
        }

        /* 卡片样式 */
        .slide-page .card {
            background: rgba(255, 255, 255, 0.05) !important;
            backdrop-filter: blur(5px) !important;
            border-radius: 12px !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            padding: 20px !important;
        }

        /* 确保图表容器正确显示 */
        .slide-page canvas {
            display: block !important;
            width: 100% !important;
            height: 200px !important;
            min-height: 200px !important;
            visibility: visible !important;
        }

        .slide-page .chart-container {
            display: block !important;
            width: 100% !important;
            height: 200px !important;
            min-height: 200px !important;
            visibility: visible !important;
        }

        /* 自动检测到的必要样式 */
        ${requiredStyles}

        @media print {
            .slide-page {
                margin: 0;
                page-break-after: always;
                page-break-inside: avoid;
            }
            .slide-page:last-child {
                page-break-after: auto;
            }
        }
    </style>
</head>
<body>
    ${slideContents}

    <!-- 内容渲染增强脚本 -->
    <script>
        // 自动检测到的必要脚本
        ${requiredScripts}

        // 重新执行所有内联脚本
        document.querySelectorAll('script:not([src])').forEach(function(oldScript) {
            const newScript = document.createElement('script');
            newScript.textContent = oldScript.textContent;
            if (oldScript.parentNode) {
                oldScript.parentNode.replaceChild(newScript, oldScript);
            }
        });

        // 执行所有收集的内联脚本
        ${Array.from(allScripts).join('\n')}

        // 设置渲染完成标志
        window.jsContentRenderingComplete = true;
    </script>
</body>
</html>`;
}

/**
 * 生成PowerPoint兼容的HTML
 */
export function generatePPTHTML(slideFiles: GeneratedFile[]): string {
  const allStyles = new Set<string>();
  const allLinks = new Set<string>();
  const allScripts = new Set<string>();
  const allExternalScripts = new Set<string>();
  let combinedHtmlContent = '';

  const slideContents = slideFiles.map((file, index) => {
    let slideContent = '';

    try {
      if (file.content.includes('<!DOCTYPE') || file.content.includes('<html')) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(file.content, 'text/html');

        // 样式
        const styles = doc.head?.querySelectorAll('style');
        styles?.forEach(style => {
          if (style.textContent) {
            allStyles.add(style.textContent);
          }
        });

        // 外部链接
        const links = doc.head?.querySelectorAll('link[rel="stylesheet"]');
        links?.forEach(link => {
          const href = link.getAttribute('href');
          if (href) {
            if (href.startsWith('http') || href.startsWith('//')) {
              allLinks.add(href);
            } else {
              try {
                const absoluteUrl = new URL(href, window.location.href).href;
                allLinks.add(absoluteUrl);
              } catch (e) {
                allLinks.add(href);
              }
            }
          }
        });

        // 内联脚本
        const scripts = doc.querySelectorAll('script:not([src])');
        scripts.forEach(script => {
          if (script.textContent) {
            allScripts.add(script.textContent);
          }
        });

        // 外部脚本
        const externalScripts = doc.querySelectorAll('script[src]');
        externalScripts.forEach(script => {
          const src = script.getAttribute('src');
          if (src) {
            if (src.startsWith('http') || src.startsWith('//')) {
              allExternalScripts.add(src);
            } else {
              try {
                const absoluteUrl = new URL(src, window.location.href).href;
                allExternalScripts.add(absoluteUrl);
              } catch (e) {
                allExternalScripts.add(src);
              }
            }
          }
        });

        slideContent = doc.body?.innerHTML || file.content;
      } else {
        slideContent = file.content;
      }
    } catch (error) {
      slideContent = file.content;
    }

    // 添加到组合HTML内容中，用于检测JS内容类型
    combinedHtmlContent += file.content;

    return `
    <div class="slide-page" data-slide="${index + 1}" id="slide-${index + 1}">
      ${slideContent}
    </div>`;
  }).join('\n');

  // 获取所需的外部库
  const requiredLibraries = getRequiredLibraries(combinedHtmlContent);
  const libraryTags = requiredLibraries.map(lib =>
    `<script src="${lib}"></script>`
  ).join('\n    ');

  // 获取所需的样式
  const requiredStyles = getRequiredStyles(combinedHtmlContent);

  // 获取所需的脚本
  const requiredScripts = getRequiredScripts(combinedHtmlContent);

  const combinedStyles = Array.from(allStyles).join('\n');
  const linkTags = Array.from(allLinks).map(href =>
    `<link href="${href}" rel="stylesheet">`
  ).join('\n    ');

  // 添加外部脚本标签
  const externalScriptTags = Array.from(allExternalScripts).map(src =>
    `<script src="${src}"></script>`
  ).join('\n    ');

  // 合并所有内联脚本
  const allInlineScripts = Array.from(allScripts).join('\n');

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示文稿 - ${slideFiles.length} 页</title>
    ${linkTags}

    <!-- 自动检测到的必要库 -->
    ${libraryTags}

    <!-- 原始外部脚本 -->
    ${externalScriptTags}

    <style id="force-scroll-styles">
        /* 全局样式覆盖 - 使用最高级别的特异性选择器 */
        html, body, html[style], body[style],
        html[class], body[class],
        html[id], body[id],
        html:not([class]), body:not([class]) {
            height: auto !important;
            min-height: 100% !important;
            max-height: none !important;
            overflow: visible !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
            scroll-behavior: smooth !important;
        }

        /* 对所有可能限制滚动的元素进行覆盖 */
        [style*="overflow: hidden"],
        [style*="overflow:hidden"],
        [style*="overflow-y: hidden"],
        [style*="overflow-y:hidden"] {
            overflow: visible !important;
            overflow-y: auto !important;
        }

        .presentation-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 20px;
        }

        .slide-page {
            width: 1280px;
            min-height: 720px;
            margin: 20px auto;
            background: white !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            page-break-after: always;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
        }

        .slide-page .slide {
            width: 1280px;
            height: 720px;
            margin: 0;
            border-radius: 0;
            position: relative;
            overflow: visible !important;
            display: block !important;
        }

        /* 确保图标和元素正确定位 */
        .slide-page .logo {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 0 auto !important;
        }

        .slide-page .feature-icon {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-shrink: 0 !important;
        }

        /* 确保标题正确显示 */
        .slide-page h1 {
            display: block !important;
            text-align: center !important;
            font-size: 3.75rem !important; /* text-6xl */
            font-weight: 700 !important;
            margin-bottom: 1.5rem !important;
            line-height: 1 !important;
        }

        .slide-page h2 {
            display: block !important;
            font-size: 1.5rem !important; /* text-2xl */
            margin-bottom: 3rem !important;
            text-align: center !important;
        }

        .slide-page h3 {
            display: block !important;
            font-size: 1.25rem !important; /* text-xl */
            font-weight: 700 !important;
        }

        /* 确保网格布局正确 - 使用具体的CSS Grid属性 */
        .slide-page .grid {
            display: grid !important;
        }

        .slide-page .grid-cols-12 {
            grid-template-columns: repeat(12, minmax(0, 1fr)) !important;
        }

        .slide-page .grid-cols-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        }

        .slide-page .grid-cols-3 {
            grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
        }

        .slide-page .col-span-4 {
            grid-column: span 4 / span 4 !important;
        }

        .slide-page .col-span-5 {
            grid-column: span 5 / span 5 !important;
        }

        .slide-page .col-span-7 {
            grid-column: span 7 / span 7 !important;
        }

        .slide-page .gap-6 {
            gap: 1.5rem !important;
        }

        .slide-page .gap-4 {
            gap: 1rem !important;
        }

        /* 确保弹性布局正确 */
        .slide-page .flex {
            display: flex !important;
        }

        .slide-page .flex-col {
            flex-direction: column !important;
        }

        .slide-page .items-center {
            align-items: center !important;
        }

        .slide-page .justify-center {
            justify-content: center !important;
        }

        .slide-page .text-center {
            text-align: center !important;
        }

        /* 卡片样式 */
        .slide-page .card {
            background: rgba(255, 255, 255, 0.05) !important;
            backdrop-filter: blur(5px) !important;
            border-radius: 12px !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            padding: 20px !important;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .nav-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .nav-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .slide-counter {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 1000;
        }

        .slide-navigation {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            z-index: 1000;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        /* 自动检测到的必要样式 */
        ${requiredStyles}

        /* 原始幻灯片样式 */
        ${combinedStyles}

        /* 强制覆盖容器背景色 - 必须在原始样式之后 */
        body, html {
            background: white !important;
            overflow-y: auto !important;
        }
        .presentation-container {
            background: white !important;
        }
        .slide-page {
            background: white !important;
        }
    </style>

    <!-- 全局样式覆盖 - 放在最后确保优先级最高 -->
    <style>
        /* 重置基本样式 */
        .presentation-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 强制启用滚动 - 放在所有样式之后以确保最高优先级 */
        html, body {
            height: auto !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
            scroll-behavior: smooth !important;
        }

        /* 确保覆盖所有可能限制滚动的样式 */
        html[style], body[style],
        html[style*="overflow"], body[style*="overflow"],
        html[style*="height"], body[style*="height"] {
            height: auto !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
        }
    </style>
</head>
<body>
    <!-- 导航控件 -->
    <div class="navigation">
        <button class="nav-button" onclick="window.print()">打印/保存为PDF</button>
        <button class="nav-button" onclick="scrollToTop()">回到顶部</button>
        <button class="nav-button" onclick="toggleFullscreen()">全屏模式</button>
    </div>

    <!-- 幻灯片计数器 -->
    <div class="slide-counter" id="slide-counter">1 / ${slideFiles.length}</div>

    <!-- 幻灯片导航 -->
    <div class="slide-navigation">
        <button class="nav-button" onclick="previousSlide()"><i class="fas fa-chevron-left"></i> 上一页</button>
        <button class="nav-button" onclick="nextSlide()"><i class="fas fa-chevron-right"></i> 下一页</button>
    </div>

    <div class="presentation-container" id="presentation">
        ${slideContents}
    </div>

    <!-- 导航和交互脚本 -->
    <script>
        // 当前幻灯片索引
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide-page');
        const totalSlides = slides.length;

        // 更新幻灯片指示器
        function updateSlideIndicator() {
            const counter = document.getElementById('slide-counter');
            if (counter) {
                counter.textContent = (currentSlideIndex + 1) + ' / ' + totalSlides;
            }
        }

        // 滚动到指定幻灯片
        function scrollToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                currentSlideIndex = index;
                slides[index].scrollIntoView({ behavior: 'smooth' });
                updateSlideIndicator();
            }
        }

        // 上一页
        function previousSlide() {
            if (currentSlideIndex > 0) {
                scrollToSlide(currentSlideIndex - 1);
            }
        }

        // 下一页
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                scrollToSlide(currentSlideIndex + 1);
            }
        }

        // 回到顶部
        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
            currentSlideIndex = 0;
            updateSlideIndicator();
        }

        // 全屏模式
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(function(err) {
                    console.log('全屏请求错误: ' + err.message);
                });
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
        }

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowDown':
                case 'ArrowRight':
                case ' ':
                    nextSlide();
                    break;
                case 'ArrowUp':
                case 'ArrowLeft':
                    previousSlide();
                    break;
                case 'Home':
                    scrollToSlide(0);
                    break;
                case 'End':
                    scrollToSlide(totalSlides - 1);
                    break;
                case 'f':
                case 'F':
                    toggleFullscreen();
                    break;
            }
        });

        // 增强JavaScript执行函数
        function enhanceJavaScriptExecution() {
            // 重新执行所有内联脚本
            document.querySelectorAll('script:not([src])').forEach(function(oldScript) {
                const newScript = document.createElement('script');
                newScript.textContent = oldScript.textContent;
                if (oldScript.parentNode) {
                    oldScript.parentNode.replaceChild(newScript, oldScript);
                }
            });

            // 处理Chart.js图表
            if (typeof Chart !== 'undefined') {
                document.querySelectorAll('canvas').forEach(function(canvas) {
                    canvas.style.display = 'block';
                    canvas.style.visibility = 'visible';
                    canvas.style.width = '100%';
                    canvas.style.minHeight = '200px';
                });

                document.querySelectorAll('.chart-container').forEach(function(container) {
                    container.style.display = 'block';
                    container.style.visibility = 'visible';
                    container.style.height = '450px';
                    container.style.width = '100%';
                    container.style.minHeight = '300px';
                });
            }

            // 处理ECharts图表
            if (typeof echarts !== 'undefined') {
                document.querySelectorAll('.echarts-container, [id*="echarts"], [class*="echarts"]').forEach(function(container) {
                    container.style.display = 'block';
                    container.style.visibility = 'visible';
                    container.style.height = '450px';
                    container.style.width = '100%';
                    container.style.minHeight = '300px';
                });
            }

            // 处理Mermaid图表
            if (typeof mermaid !== 'undefined') {
                mermaid.initialize({ startOnLoad: true });
            }
        }

        // 强制启用滚动功能的函数
        function forceEnableScrolling() {
            // 强制覆盖html和body元素的样式
            document.documentElement.style.setProperty('overflow-y', 'auto', 'important');
            document.documentElement.style.setProperty('height', 'auto', 'important');
            document.documentElement.style.setProperty('max-height', 'none', 'important');
            document.documentElement.style.setProperty('overflow', 'visible', 'important');

            document.body.style.setProperty('overflow-y', 'auto', 'important');
            document.body.style.setProperty('height', 'auto', 'important');
            document.body.style.setProperty('max-height', 'none', 'important');
            document.body.style.setProperty('overflow', 'visible', 'important');
            document.body.style.setProperty('overflow-x', 'hidden', 'important');
            document.body.style.setProperty('scroll-behavior', 'smooth', 'important');

            // 删除所有可能限制滚动的样式表
            document.querySelectorAll('style').forEach(function(styleEl) {
                const styleContent = styleEl.textContent || '';
                if (styleContent.includes('overflow: hidden') ||
                    styleContent.includes('overflow:hidden') ||
                    styleContent.includes('overflow-y: hidden') ||
                    styleContent.includes('overflow-y:hidden')) {

                    // 修改样式内容而不是删除元素
                    styleEl.textContent = styleContent
                        .replace(/overflow\s*:\s*hidden/g, 'overflow: visible !important')
                        .replace(/overflow-y\s*:\s*hidden/g, 'overflow-y: auto !important');
                }
            });

            // 确保所有幻灯片可见
            document.querySelectorAll('.slide-page, .slide').forEach(function(slide) {
                slide.style.display = 'block';
                slide.style.visibility = 'visible';
            });

            // 添加一个新的强制样式表
            let forceStyle = document.getElementById('force-scroll-override');
            if (!forceStyle) {
                forceStyle = document.createElement('style');
                forceStyle.id = 'force-scroll-override';
                document.head.appendChild(forceStyle);
            }

            forceStyle.textContent = '\
                html, body, html[style], body[style] {\
                    height: auto !important;\
                    min-height: 100% !important;\
                    max-height: none !important;\
                    overflow: visible !important;\
                    overflow-y: auto !important;\
                    overflow-x: hidden !important;\
                    scroll-behavior: smooth !important;\
                }\
                \
                .slide-page, .slide {\
                    display: block !important;\
                    visibility: visible !important;\
                }\
                \
                /* 覆盖所有可能的隐藏滚动条的选择器 */\
                *[style*="overflow: hidden"],\
                *[style*="overflow:hidden"],\
                *[style*="overflow-y: hidden"],\
                *[style*="overflow-y:hidden"] {\
                    overflow: visible !important;\
                    overflow-y: auto !important;\
                }\
            ';
        }

        // 设置MutationObserver来监控DOM变化
        function setupScrollObserver() {
            // 创建MutationObserver实例
            const observer = new MutationObserver(function(mutations) {
                let needsScrollFix = false;

                // 检查是否有样式变化
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' &&
                        (mutation.target === document.documentElement || mutation.target === document.body)) {
                        if (mutation.attributeName === 'style') {
                            needsScrollFix = true;
                        }
                    } else if (mutation.type === 'childList') {
                        // 检查是否有新样式表添加
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeName === 'STYLE') {
                                needsScrollFix = true;
                            }
                        });
                    }
                });

                // 如果需要，重新应用滚动修复
                if (needsScrollFix) {
                    forceEnableScrolling();
                }
            });

            // 开始监控整个文档
            observer.observe(document.documentElement, {
                attributes: true,
                childList: true,
                subtree: true,
                attributeFilter: ['style', 'class']
            });

            return observer;
        }

        // 在页面加载完成后执行增强功能
        window.addEventListener('DOMContentLoaded', function() {
            try {
                // 执行自动检测到的必要脚本
                ${requiredScripts}

                // 执行所有收集到的内联脚本
                ${allInlineScripts}

                enhanceJavaScriptExecution();
                // 初始化幻灯片指示器
                updateSlideIndicator();

                // 立即应用滚动修复
                forceEnableScrolling();

                // 设置滚动监控器
                const scrollObserver = setupScrollObserver();

                // 定时强制启用滚动
                setTimeout(forceEnableScrolling, 500);
                setTimeout(forceEnableScrolling, 1000);
                setTimeout(forceEnableScrolling, 2000);

                // 监听滚动事件，更新幻灯片计数器
                window.addEventListener('scroll', function() {
                    // 找出当前可见的幻灯片
                    const slides = document.querySelectorAll('.slide-page');
                    let closestSlide = 0;
                    let minDistance = Infinity;

                    slides.forEach(function(slide, index) {
                        const rect = slide.getBoundingClientRect();
                        const distance = Math.abs(rect.top);
                        if (distance < minDistance) {
                            minDistance = distance;
                            closestSlide = index;
                        }
                    });

                    // 更新当前幻灯片索引
                    if (currentSlideIndex !== closestSlide) {
                        currentSlideIndex = closestSlide;
                        updateSlideIndicator();
                    }
                });
            } catch (error) {
                console.error('脚本执行错误:', error);
            }
        });

        // 页面完全加载后再执行一次
        window.addEventListener('load', function() {
            forceEnableScrolling();

            // 添加点击事件处理器，确保用户交互后仍然可以滚动
            document.addEventListener('click', function() {
                setTimeout(forceEnableScrolling, 100);
            });
        });
    </script>
</body>
</html>`;
}